<!DOCTYPE html>
<html>

<head>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>A-Knowledge 实例库管理与智能应用</title>
    <meta charset="utf-8"/>

    <link href="../../content/css/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="../../content/css/index.css" rel="stylesheet"/>
    <link href="../../content/css/main.css" rel="stylesheet"/>
    <link href="SlkHome.css" rel="stylesheet"/>
    <link href="SlkList.css" rel="stylesheet"/>
    <link rel="shortcut icon" href="../../ico/tab/v2.png" />
    <style>
        #slk :hover {
            color: #63b0ff;
        }

        .tabelhover :hover {
            text-decoration: underline;
        }
        .el-tabs__item {
            padding: 0 37.5px !important;
        }

        .nav-container {
            display: flex;
            padding: 10px;
            border-radius: 5px;
        }
        .nav-button {
            display: flex;
            align-items: center;
            margin: 4px 5px;
            /* padding: 10px 20px; */
            padding-left: 18px;
            padding-right: 18px;
            height: 30px;
            font-weight: bold;
            border: none;
            border-radius: 35px;
            background-color: #7bbdff;
            color: #ffffff;
            cursor: pointer;
            transition: background-color 0.3s, color 0.3s;
        }
        .nav-button:hover {
            background-color: #ffffff;
            color: #000000;
        }
        .active {
            background-color: #4a90e2;
            color: #ffffff;
        }
    </style>
    <style scoped>
        .main-menu {
            margin-left: auto;
            display: block;
            height: 58px;
        }

        .main-menu ul {
            display: -webkit-flex;
            display: -ms-flex;
            display: flex;
            height: 38px;
            padding: 22px 8px;
            margin: 0;
            list-style-type: none;
        }

        .main-menu ul li {
            margin-right: 15px;
            margin-left: 15px;
            text-transform: uppercase;
            font-size: 17px;
            line-height: 28px;
            height: 28px;
            font-weight: bold;
            position: relative;
            font-family: "微软雅黑", "宋体", "Helvetica Neue", Arial, Helvetica, sans-serif;
        }


        .main-menu ul li a:hover {
            /*color: darkblue;*/
            color:white;
        }

        .main-menu ul li:hover {
            border-bottom: 3px solid white;
        }
        .current {
            /*border-bottom: 4px solid darkslateblue;*/
            border-bottom: 3px solid white;
        }

        .current a {
            color: white;
        }

        .menu-wrapper {
            display: -webkit-flex;
            display: -ms-flex;
            display: flex;
            -ms-flex-align: center;
            align-items: center;
        }

        .active {
            background-color: #409EFF;
            border-radius: 5px;
        }

        a {
            color: white;
        }

        .right-menu-container {
            width: 100px;
        }

        .right-menu-container:hover {
            width: 100px;
        }

        .el-radio-button__inner{
            width:116px;
        }


    </style>
</head>

<body>

<div id="app" v-cloak style="overflow: hidden;">

    <el-row >

        <el-col  style="z-index:101;width:100%;height:54px;">

            <div class="nav-container" style="position: absolute;right: 7%;margin-top: 3px;">
                <el-button class="nav-button" @click="openSlkyl">实例库预览</el-button>
                <el-button class="nav-button" @click="openSlkgl">实例库管理</el-button>
                <el-button class="nav-button" @click="openZswd">知识问答</el-button>
                <el-button class="nav-button" @click="openCgtj">成果推荐</el-button>
                <el-button class="nav-button" @click="openGltj">规律统计</el-button>
                <el-button class="nav-button" @click="openXtgl">系统管理</el-button>
            </div>

            <div style="position: absolute;right: 1%;margin-top: 18px;">
                <img src="../../content/assets/img/but1.png"
                     style="width: 25px;height: 25px;margin-left: 5px" />
                <img src="../../content/assets/img/but3.png"
                     style="width: 25px;height: 25px;margin-left: 5px" />
                <img src="../../content/assets/img/but2.png"
                     style="width: 25px;height: 25px;margin-left: 5px" />
            </div>

            <img src="../../content/assets/img/top.jpg"
                 style="width: 100%;height: 60px" />

        </el-col>
    </el-row>

    <template>

        <!--        <div style="float:right;margin-top:10px;margin-right: 10px;z-index: 999">-->


            <el-input id="slmcBtn" size="small" v-model="name" placeholder="输入实例名称" class="search_key"
                      style="right: 160px; margin-top: 7px;position: absolute;z-index: 999"></el-input>
            <el-button id="cxBtn" type="primary" size="small" @click="searchresult"
                       style="right: 97px; margin-top: 7px;position: absolute;z-index: 999">查询
            </el-button>
            <el-button id="gjsxBtn" type="primary" size="small" @click="openSearch()"
                       style="right: 10px; margin-top: 7px;position: absolute;z-index: 999">高级筛选
            </el-button>
            <!--        </div>-->
<!--            <el-button id="xjslkBtn" size="small" type="primary"-->
<!--                       style="height: 25px;margin-left: 274px;margin-top: 10px;position: absolute;z-index: 999;display: flex;align-items: center;" @click="openWells()">-->
<!--                新建实例库-->
<!--            </el-button>-->
<!--            <el-button id="cgcjBtn" size="small" type="primary"-->
<!--                       style="height: 25px;margin-left: 380px;margin-top: 10px;position: absolute;z-index: 999;display: flex;align-items: center;" @click="openCgcj()">-->
<!--                成果采集-->
<!--            </el-button>-->
<!--        <el-tabs v-model="isShow" @tab-click="handleClick()" type="border-card" style="margin-top: -12px;margin-left: -12px">-->

        <el-tabs v-model="isShow" @tab-click="handleClick()" type="border-card" style="margin: 5px 5px;">
            <el-tab-pane label="地  图" name="地图" style="margin-left: -10px;margin-top: -10px;width: 101%">

                <el-row id="rowDt" style="z-index:101;width:100%;background-color:white;">
                    <el-row class="container">


                        <div style="min-width:120px;" class="menu-wrapper">
                            <template>
                                <el-card class="box-card">
                                    <div slot="header" class="clearfix" style="">
                                        <el-input @focus="showSlkcard" style="width: 248px;"
                                                  size="mini"
                                                  v-model="name"
                                                  placeholder="搜索实例" @keyup.enter.native="searchresultGis()"></el-input>
                                        <el-button size="mini" type='primary' @click="searchresultGis">搜索</el-button>
                                        <el-button size="mini" type="primary"  plain
                                                   @click="openSearchGis()">高级筛选
                                        </el-button>

                                    </div>
                                </el-card>
                                <el-card v-show="slkcard" class="box-card-popup">

                                    <el-button
                                            style="z-index:999;position: fixed;top: 182px;left: 425px;margin-top: -11px;"
                                            @click="stopGet" type="text">
                                        <i class="el-icon-close"></i>
                                    </el-button>

                                    <el-table :row-style="{height:35+'px',cursor:'pointer'}"
                                              :cell-style="{padding:0+'px'}" class="el-card-table"
                                              @row-click="openDetails" :data="tableData" height="620"
                                              style="margin-top: -20px;margin-left: -20px">

                                        <el-table-column label="序号" type="index" width="50" align="center">
                                        </el-table-column>
                                        <el-table-column prop="oilName" label="实例名称" sortable width="180"
                                                         header-align="center">
                                            <template slot-scope="scope">
                                                <el-link>{{ scope.row.oilName }}</el-link>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="xzdw" label="单位" width="110" sortable
                                                         header-align="center"></el-table-column>
                                        <el-table-column align="right" label="操作" width="66">
                                            <template slot-scope="scope">
                                                <el-button
                                                        size="mini"
                                                        @click="rowClickGis(scope.row)">打开
                                                </el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>

                                </el-card>
                                <el-card v-show="datacard" class="box-card-data">
                                    <div style="border-bottom: 1px dashed rgb(201, 201, 201);margin: 0px 0px; margin-top: -11px; padding-bottom: 3px;margin-left: -10px;height: 24px;width: 284px">
                                        <span style="font-weight:bold;font-size: 15px;color: #000000;margin-left: 6px;"> 实例库基本信息</span>
                                        <!--                                <el-button style="float: right;margin-top: -4px;margin-right: 8px;"-->
                                        <!--                                        size="mini" icon="el-icon-more-outline"-->
                                        <!--                                        @click="rowClick()">详细</el-button>-->

                                        <div style="text-align: center;text-align: right;margin-right: 5px;margin-top: -23px;display: none;">
                                            <el-button type="text" @click="openGd()" size="small">更多</el-button>
                                            <!--                                    <span style="color: #409EFF;font-size: 13px" @click="rowClick()">详细</span>-->
                                        </div>
                                    </div>
                                    <!--                            <el-divider class="el-divider-title"></el-divider>-->
                                    <div style="margin-top: 5px;width: 274px">
                                        <li style="list-style-type:none;line-height: 17px;margin-left: -10px;"
                                            v-for="(item, index) in slkList">
                                            <span style="text-align: right; display: inline-block;width: 120px;"> {{item.name}}</span>
                                            <el-tooltip :content="item.value" placement="bottom">
                                                <span>{{ item.value | ellipsis}}</span>
                                            </el-tooltip>
                                            <el-divider class="el-divider"></el-divider>
                                        </li>

                                        <!--                                <li style="list-style-type:none;" >-->
                                        <!--                                    <div style="text-align: center;text-align: right;margin-right: 5px;margin-top: -7px;">-->
                                        <!--&lt;!&ndash;                                        <span @click="openGd" style="color: #409EFF;font-size: 13px">更多</span>&ndash;&gt;-->
                                        <!--                                        <el-button type="text"@click="openGd()" size="small" >更多</el-button>-->
                                        <!--                                    </div>-->
                                        <!--                                </li>-->
                                    </div>

                                </el-card>
                                <el-card v-show="wellcard" class="box-card-data">
                                    <div style="border-bottom: 1px dashed rgb(201, 201, 201);margin: 0px 0px; margin-top: -11px; padding-bottom: 3px;margin-left: -4px;height: 24px;">
                                        <span style="font-weight:bold;font-size: 15px;color: #000000">井信息</span>
                                    </div>

                                </el-card>
                                <el-card v-show="DCcard" class="box-card-DC" style="z-index: 999">
                                    <iframe style="width: 285px;border: 0px;height:445px;margin-top: -21px;margin-left: -10px;"
                                            id="DCfrmmain"
                                            src=""></iframe>
                                </el-card>
                                <el-card v-show="infocard" class="box-card-info">
                                    <div style="border-bottom: 1px dashed rgb(201, 201, 201);margin: 0px 0px; margin-top: -11px; padding-bottom: 3px;margin-left: -10px;height: 24px;width: 400px">

                                        <el-link type="info" @click="rowClickGis()"><span
                                                style="font-weight:bold;font-size: 15px;color: #000000;margin-left: 6px;">{{infoName}}</span>
                                        </el-link>


                                        <div style="text-align: center;text-align: right;margin-right: 5px;margin-top: -30px;">
                                            <el-button type="text" @click="rowClickGis()" size="small"
                                                       style="margin-right: 10px;">详细
                                            </el-button>
                                            <el-button type="text" @click="infoClick()" size="small"
                                                       icon="el-icon-close"></el-button>
                                        </div>
                                    </div>
                                    <!--                            <el-divider class="el-divider-title"></el-divider>-->
                                    <div style="margin-top: 5px;width: 400px">
                                        <div style="height: 340px;margin-top: 10px">
                                            <div style="font-weight: bold">成果</div>
                                            <!--                                    <li style="list-style-type:none;line-height: 17px;margin-left: 12px;margin-top: 8px;float: left" v-for="(item, index) in imgList">-->
                                            <!--                                        <img  v-bind:src="item.content" width="110px" height="60px" @click="imgLogo(item)">-->
                                            <!--                                    </li>-->
                                            <div style="width: 380px">
                                                <li style="list-style-type:none;line-height: 17px;margin-left: 20px;margin-top: 8px;float: left">
                                                    <img src="../../upload/cg/users/0a0ab4ba-c9e9-4ef1-b3fe-445aca5a16a6.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/1e95d9a0-aa19-433b-b1f5-42ba0bf9455f.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/2b3986f1-d918-4cd7-b880-a8c70f903f07.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/2fe42089-346c-4477-9e2e-3281b6ab41e8.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/4c4449bf-b0a9-424a-a348-9c14cbeb49c4.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/4ade37e1-b654-4a91-9f96-9daac28907c2.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/36b45c03-6276-45fb-b60b-f36d7af783ae.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/68f92005-081a-4e99-84e6-a350e7538e87.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/0529f537-3f18-4d3d-a816-b2f5897848f6.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/552ce825-864d-4906-931c-6e42b6779fa9.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/ajr_07f09c0d953447a4ae97a88147b4c267.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/ajr_7a9f2603485d4ec6aefd8595d6d19e65.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/ajr_7bb8f01cbdbe41c5b1caf28851d342af.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/ffe75fcb-fdf8-451d-8c14-1c41533d49ba.png"
                                                         width="110px" height="60px">
                                                    <img src="../../upload/cg/users/dd46a30d-9fa4-4509-87b0-1e3993040e5c.png"
                                                         width="110px" height="60px">
                                                </li>
                                            </div>
                                        </div>
                                        <div style="height: 180px;margin-top: 10px">
                                            <div style="font-weight: bold">简介</div>
                                            <!--                                    ;margin-left: 10px;margin-top: 8px-->
                                            <!--                                    <li style="list-style-type:none;line-height: 50px" v-for="(item, index) in infoList">-->
                                            <!--                                        <span>{{(index+1)+"、"+item.content}}</span>-->
                                            <!--                                    </li>-->
                                            <div style="text-indent: 20px;width: 380px;line-height: 20px;text-align: justify;margin-top: 10px">
                                                从我国近些年来对低渗透油田的研究和开发水平看，有了较大进展和提高，但与中高渗透油田相比仍有较大的差距，我国低渗透油田平均采收率只有21.4％，比重高渗透油田34％低12.6个百分点。目前有五十多个油田（区块）年开采速度小于0.5％，预测最终采收率只有15.5％1.油气藏精细表征
                                            </div>
                                            <!--                                    <li style="list-style-type:none;line-height: 20px;margin-top: 10px;" >-->
                                            <!--&lt;!&ndash;                                         <span style="margin-right: 5px">从我国近些年来对低渗透油田的研究和开发水平看，有了较大进展和提高，但与中高渗透油田相比仍有较大的差距，我国低渗透油田平均采收率只有21.4％，比重高渗透油田34％低12.6个百分点。目前有五十多个油田（区块）年开采速度小于0.5％，这些低速低效油田（区块）的地质储量约3.2108t，其平均采油速度仅0.27％，预测最终采收率只有15.5％1.油气藏精细表征</span>&ndash;&gt;-->
                                            <!--                                    </li>-->
                                        </div>
                                    </div>

                                </el-card>

                            </template>
                        </div>
                        <iframe name="frmmain"
                                id="frmmain"
                                :src="url"
                                width="100%"
                                v-bind:height="tableHeight"
                                scrolling="no"
                                frameborder="0">
                        </iframe>



                    </el-row>

                    <el-dialog title="查看" :visible.sync="dialogFormVisibleImg" style="top:130px;">
                        <img v-bind:src="imgUrl" height="300px" width="500px" style="margin-left: 100px">
                    </el-dialog>
                    <el-dialog
                            title="提示"
                            :visible.sync="centerDialogVisible"
                            width="30%"
                            center
                    >
                        <span style="margin-left: 100px">请点击地图选择实例库位置</span>
                        <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="allowToCast">{{content}}</el-button>
                    </span>
                    </el-dialog>
                </el-row>

            </el-tab-pane>
            <el-tab-pane label="列  表" name="列表">

                <el-row id="rowLb" style="z-index:101;width:100%;background-color:white;">
                    <el-main style="padding-bottom: 0px;margin-top: -20px;">
                        <el-table :row-style="{height:35+'px'}" :cell-style="{padding:0+'px'}" :data="tableData"
                                  v-loading="loading" style="width: 100%;border-color: #c6e2ff;"
                                  height="calc(100vh - 38px)">
                            <!--                      height="calc(100vh - 238px)" @row-click="rowClick">-->
                            <el-table-column label="序号" min-width="3.3%">
                                <template slot-scope="{$index}">
                                    <span>{{(pageNo - 1) * 20 + $index + 1}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="oilName" label="实例名称" min-width="16.2%" sortable>
                                <template scope="scope">

                                    <el-button type="text" @click="rowClick(scope.row)">
                                        <el-link style="color: #409EFF">{{ scope.row.oilName }}</el-link>
                                    </el-button>
                                    <!--                        <div style="color: #00F;text-decoration: underline" @click="rowClick(scope.row)">{{ scope.row.oilName }}</div>-->
                                </template>
                            </el-table-column>
                            <el-table-column prop="xzdw" label="单位" min-width="6.7%" min-width="12%" sortable></el-table-column>
                            <el-table-column prop="sj" label="时间" min-width="6.7%" sortable></el-table-column>
                            <el-table-column prop="yqclx" label="油气藏类型" min-width="8.1%" sortable></el-table-column>
                            <el-table-column prop="dzcl" label="地质储量(10^4t)" min-width="7.4%" sortable></el-table-column>
                            <el-table-column prop="hymj" label="含油面积(km 2)" min-width="7.4%" sortable></el-table-column>
                            <el-table-column prop="dxyynd" label="地下原油粘度(mPa.s)" min-width="9%" sortable></el-table-column>
                            <el-table-column prop="dmyynd" label="地面原油粘度(mPa.s)" min-width="9%" sortable></el-table-column>
                            <el-table-column prop="pjkxd" label="平均孔隙度(%)" min-width="8.1%" sortable></el-table-column>
                            <el-table-column prop="pjstl" label="平均渗透率(10^(-3)μ㎡)" min-width="8.1%" sortable></el-table-column>
                            <el-table-column prop="yshybhd" label="原始含油饱和度(%)" min-width="10.13%" sortable></el-table-column>
                        </el-table>
                    </el-main>
                    <!--        <el-footer>-->
                    <!--            <el-pagination background layout="prev,pager,next" :total="total" :page-size="pageSize"-->
                    <!--                           @current-change="changePage($event)"-->
                    <!--                           style="margin-top: 20px;padding: 0;display: flex;justify-content: center;"></el-pagination>-->
                    <!--        </el-footer>-->
                    <el-dialog :title="title" :visible.sync="searchFormVisible" width="1040px" class="abow_dialog"
                               append-to-body>
                        <el-form :model="searchModel" ref="ruleForm">
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="油气藏类型">
                                        <el-select size="mini" style="width:130px;" v-model="judge1" disabled placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>

                                        <el-select size="mini" style="width:130px;" v-model="searchModel.yqclx"
                                                   placeholder="请选择">
                                            <el-option v-for="item in searchDictArray" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="油藏开采方式">
                                        <el-select size="mini" style="width:130px;" v-model="judge2" disabled placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.yckcfs"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="单元产生年月" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge3" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.dycsny"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="开发年月" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge4" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.kfny"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="注水年月" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge5" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.zsny"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="注汽年月" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge6" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.zqny1"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="注气年月" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge7" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.zqny"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="注汽年月" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge8" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.zqny2"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="火烧年月" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge9" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.hsny"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="汽驱年月" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge10" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.qqny"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="开发层系" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge11" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.kfcx"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="投入开发年月" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge12" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.trkfny"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="原始地层压力(MPa)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge13" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.ysdcyl"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="年度" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge14" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.nd"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="地层压差(MPa)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge15" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.dcyc"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="目前地层要(MPa)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge16" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.mqdcy"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="井网密度(Well/km²)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge17" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.jwmd"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="含油面积(Km²)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge18" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.hymj"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="水驱储量(10⁴t)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge19" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.sqcl"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="地质储量(10⁴t)/(10⁸m³)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge20" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.dzcl"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="热采储量(10⁴t)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge21" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.rccl"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="汽驱储量(10⁴t)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge22" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.qqcl"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="溶解气可采储量(10⁸m³)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge23" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.rjqkccl"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="溶解气地质储量(10⁸m³)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge24" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.rjqdzcl"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="水驱控制储量(10⁴t)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge25" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.sqkzcl"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="可采储量(10⁴t)/(10⁸m³)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge26" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.kccl"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="标定采收率(%)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge27" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.bdcsl"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="水驱动用储量(10⁴t)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge28" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.sqdycl"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="气换算系数" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge29" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.qhsxs"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="油换算系数" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge30" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.yhsxs"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="标定日产油量(t)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge31" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.bdrcyl"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="剩余可采储量(10⁴t)/(10⁸m³)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge32" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.sykccl"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="标定日产气量(10⁴m³)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge33" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.bdrcql"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="标定核实日产油(气)量(10⁴t)/(10⁸m³)"
                                                  style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge34" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.bdhsrcyl"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="动态注采对应率(%)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge35" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.dtzcdyl"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label-width="240px" label="静态注采对应率(%)" style="margin-top:-10px">
                                        <el-select size="mini" style="width:130px;" v-model="judge36" placeholder="请选择">
                                            <el-option v-for="item in judgeOptions" :key="item.value" :label="item.label"
                                                       :value="item.value"></el-option>
                                        </el-select>
                                        <el-input style="width:130px;" size="mini" v-model="searchModel.jtzcdyl"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <div slot="footer" class="dialog-footer">
                            <el-button @click="searchFormVisible = false">取 消</el-button>
                            <el-button @click="clearSearch">重 置</el-button>
                            <el-button type="primary" @click="closeDialog">确 定</el-button>
                        </div>
                    </el-dialog>

                </el-row>


            </el-tab-pane>
        </el-tabs>





    </template>


</div>
</body>
</html>
<script>
    $.cookie("username", "chengrui");
</script>

<script src="../../content/js/jquery-1.10.2.js"></script>
<script src="../../content/js/vue.js"></script>
<script src="../../content/js/index.js"></script>
<script src="../../content/js/Cookie.js"></script>
<script src="../../content/js/common.js"></script>
<script src="../../content/libs/layer/layer.js"></script>
<script src=""></script>
<script type="text/javascript">

    function logout1() {
        __delCookie("Authorization");
        __delCookie("Permission");
        location.href = "../../html/login_slk.html";
    }

    var projectId = getQueryString("projectId");
    var isShow = getQueryString("isShow");
    var timeId;
    var la;
    var lo;

    var p = getPagePermission("", "003", true);

    let app = new Vue({
        el: '#app',
        filters: {
            ellipsis(value) {
                if (!value) return "";

                if (/.*[\u4e00-\u9fa5]+.*$/.test(value)) {
                    if (value.length > 9) {
                        return value.slice(0, 9) + "...";
                    }
                } else {
                    if (value.length > 18) {
                        return value.slice(0, 18) + "...";
                    }
                }


                return value;
            },
        },
        data: function () {
            return {
                activeName: '地图',
                isShow: '地图',
                formLabelWidth: '120px',
                total: 0,
                pageNo: 1,
                pageSize: 20,
                tableHeight: window.innerHeight - 55,
                jdHeight: 360,
                dataScroll: [],
                newSlk: false,
                dialogFormVisibleImg: false,
                slkcard: true,
                datacard: false,
                DCcard: false,
                content: null,
                wellcard: false,
                infocard: false,
                infoName: null,
                jdData: 0,
                name: '',
                tableData: [],
                centerDialogVisible: false,
                dialogRule: {
                    longitude: [{
                        required: true,
                        message: "请输入经度",
                        trigger: "change"
                    }],
                    latitude: [{
                        required: true,
                        message: "请输入纬度",
                        trigger: "change"
                    }],
                    slkname: [{
                        required: true,
                        message: "请输入实例库名称",
                        trigger: "change"
                    }],
                },
                options: [{
                    value: 'yj',
                    label: '油井'
                }, {
                    value: 'qj',
                    label: '气井'
                }, {
                    value: 'sj',
                    label: '水井'
                }],

                url: '',
                DCurl: '',
                xzdw: '',
                sj: '',
                yqclx: '',
                dzcl: '',
                hymj: '',
                dxyynd: '',
                dmyynd: '',
                pjkxd: '',
                pjstl: '',
                yshybhd: '',


                judge_xzdw: '',
                judge_sj: '',
                judge_yqclx: '',
                judge_dzcl: '',
                judge_hymj: '',
                judge_dxyynd: '',
                judge_dmyynd: '',
                judge_pjkxd: '',
                judge_pjstl: '',
                judge_yshybhd: '',

                longitude: '118.219',
                latitude: '37.490',
                rowid: '',
                slkList: [],
                infoList: [],
                imgList: [],
                imgUrl: null,
                seconds: 3,

                title: '',
                total: 0,
                pageNo: 1,
                pageSize: 20,
                loading: false,
                tableData: [],
                name: '',
                // editFormVisible: false, //编辑界面是否显示
                searchFormVisible: false,
                editLoading: false,

                searchModel: {
                    yqclx: null,
                    dzcl: null,
                    hymj: null,
                    dycsny: null,
                    zsny: null,
                    zqny: null,
                    hsny: null,
                    kfcx: null,
                    ysdcyl: null,
                    dcyc: null,
                    jwmd: null,
                    sqcl: null,
                    rccl: null,
                    rjqkccl: null,
                    sqkzcl: null,
                    bdcsl: null,
                    qhsxs: null,
                    bdrcyl: null,
                    bdrcql: null,
                    dtzcdyl: null,
                    yckcfs: null,
                    kfny: null,
                    zqny1: null,
                    zqny2: null,
                    qqny: null,
                    trkfny: null,
                    nd: null,
                    mqdcy: null,
                    qqcl: null,
                    rjqdzcl: null,
                    kccl: null,
                    sqdycl: null,
                    yhsxs: null,
                    sykccl: null,
                    bdhsrcyl: null,
                    jtzcdyl: null,
                },

                judgeOptions: [{
                    value: '>',
                    label: '大于'
                }, {
                    value: '<',
                    label: '小于'
                }, {
                    value: '=',
                    label: '等于'
                }, {
                    value: '>=',
                    label: '大于等于'
                }, {
                    value: '<=',
                    label: '小于等于'
                }],
                searchDictArray: [],//字典下拉框
                judge1: '=', judge2: '=',
                judge3: '', judge4: '', judge5: '', judge6: '', judge7: '',
                judge8: '', judge9: '', judge10: '', judge11: '', judge12: '',
                judge13: '', judge14: '', judge15: '', judge16: '', judge17: '',
                judge18: '', judge19: '', judge20: '', judge21: '', judge22: '',
                judge23: '', judge24: '', judge25: '', judge26: '', judge27: '',
                judge28: '', judge29: '', judge30: '', judge31: '', judge32: '',
                judge33: '', judge34: '', judge35: '', judge36: ''

            }
        },
        mounted() {
            // $('#datacenter')[0].src = 'DataCenter.html?projectid=' + project_id + str;
            this.jdHeight = window.innerHeight - 200;
            this.getData();//获取表格数据
            this.initMap(); //初始化地图
            window.gisDyClick = this.gisDyClick;
            window.gisDjClick = this.gisDjClick;
            window.gisEmptyClick = this.gisEmptyClick;


            this.getSearchDictArray()
            $("#rowDt").show();//显示div

            $("#xjslkBtn").show();//显示div


            $("#slmcBtn").hide();//隐藏div
            $("#cxBtn").hide();//隐藏div
            $("#gjsxBtn").hide();//隐藏div

            if(isShow){
                this.isShow = isShow
            }

        },
        methods: {
            openZswd(){
                window.open("http://81.68.240.238:16881/ui/chat/037057f06472a5e8");
            },
            openCgtj(){
                window.open("aisearch/search.html");
            },
            openSlkyl(){
                window.open("SlkHome.html");
            },
            openSlkgl(){
                window.open("Slkgl.html");
            },
            openGltj(){
                window.open("SlkGltj.html");
            },
            openXtgl(){
                window.open("../sys/setting.html");
            },

            cutMap: function () {
                if (this.isShow == '地图') {
                    $("#gisDiv").show();//显示div
                    // document.getElementById('gisAndTable').style.top='6px';
                    this.url = "Gis/gis.html?longitude=" + this.longitude + "&latitude=" + this.latitude;
                } else {
                    $("#gisDiv").hide();//隐藏div
                    this.url = "SlkList.html";
                    // document.getElementById('gisAndTable').style.top='6px';
                }
            },
            handleClick: function () {

                if (this.isShow == '地图') {

                    $("#xjslkBtn").show();//显示div

                    $("#slmcBtn").hide();//隐藏div
                    $("#cxBtn").hide();//隐藏div
                    $("#gjsxBtn").hide();//隐藏div

                    $("#rowDt").show();//显示div

                    $("#rowLb").hide();//隐藏div
                    // this.url = "Gis/gis.html?longitude=" + this.longitude + "&latitude=" + this.latitude;
                } else {
                    // $("#xjslkBtn").hide();//隐藏div
                    // this.url = "SlkList.html";

                    $("#slmcBtn").show();//显示div
                    $("#cxBtn").show();//显示div
                    $("#gjsxBtn").show();//显示div

                    $("#rowDt").hide();//隐藏div
                    $("#rowLb").show();//显示div
                }
            },
            openGd: function () {
                window.open("ht_zttable.html");
            },
            //空白 地图点击事件
            gisEmptyClick: function () {
                var that = this;
                this.datacard = false
                this.DCcard = false
                this.wellcard = false
                this.infocard = false
                // $("#gisAndTable").show();//显示div

                if (this.newSlk) {
                    this.newSlk = false;
                    clearTimeout(timeId);
                    timeId = setTimeout(function () {
                        var _this = this;
                        var index = layer.open({
                            type: 2,
                            skin: 'layui-layer-lan',
                            shade: false,
                            maxmin: true,
                            // scrollbar: false,
                            title: "新建实例库",
                            area: ['780px', '428px'],
                            btn: ['确定', '关闭'],
                            content: ["SlkNew.html?lo=" + lo + "&la=" + la, "yes"],
                            yes: function (index, layero) {

                                var loadIndex = layer.load(1);

                                var ifrm = layero.find("iframe")[0].contentWindow;
                               var contrast= ifrm.app.contrast
                                if (!contrast){
                                    layer.msg('实例库名称有重复，不能添加！')
                                    layer.close(loadIndex);
                                    return
                                }
                                var oilName = ifrm.app.form.oilName;
                                var xzdw = ifrm.app.form.xzdw;
                                var sj = ifrm.app.form.sj;
                                var yqclx = ifrm.app.form.yqclx;
                                var dzcl = ifrm.app.form.dzcl;
                                var hymj = ifrm.app.form.hymj;
                                var dxyynd = ifrm.app.form.dxyynd;
                                var dmyynd = ifrm.app.form.dmyynd;
                                var pjkxd = ifrm.app.form.pjkxd;
                                var pjstl = ifrm.app.form.pjstl;
                                var yshybhd = ifrm.app.form.yshybhd;
                                var longitude = ifrm.app.form.longitude;
                                var latitude = ifrm.app.form.latitude;
                                var mbId=ifrm.app.form.mb
                                var sParams = {
                                    mbId: mbId,
                                    oilName: oilName,
                                    xzdw: xzdw,
                                    sj: sj,
                                    yqclx: yqclx,
                                    dzcl: dzcl,
                                    hymj: hymj,
                                    dxyynd: dxyynd,
                                    dmyynd: dmyynd,
                                    pjkxd: pjkxd,
                                    pjstl: pjstl,
                                    yshybhd: yshybhd,
                                    longitude: longitude,
                                    latitude: latitude,
                                };
                                ajr_ajax_post("/slk/saveSlkInfo", sParams, (data) => {
                                    if (data.code == "0" || data.code == 0) {
                                        var childWindow = $("#frmmain")[0].contentWindow;
                                        var opt = {
                                            x: longitude,
                                            y: latitude
                                        }
                                        // childWindow.setMapCenter(opt)
                                        childWindow.addUnit(oilName, data.data.id, longitude, latitude)
                                        layer.close(loadIndex);
                                        layer.close(index);
                                        layer.msg('新建实例库成功！')
                                        that.getData();
                                    } else {
                                        layer.msg('新建实例库失败！')
                                        that.getData();
                                    }

                                }, (e) => {
                                    console.log(e);
                                });
                            },
                        });
                    }, 250);
                }
            },
            //单井  地图点击事件
            gisDjClick: function () {
                this.datacard = false
                this.DCcard = false
                // this.wellcard = true
                this.infocard = false
                // $("#gisAndTable").hide();//隐藏div
                // var childWindow = $("#frmmain")[0].contentWindow;
                // childWindow.initDjData()
            },
            //单元 地图点击事件
            // gisDyClick : function (val,name){
            //     this.infoName = name
            //     ajr_ajax_get("/slk/getSlkInfoAndCg", {projectId:val}, (data) => {
            //         if (data.code == "0" || data.code == 0) {
            //             // var childWindow = $("#frmmain")[0].contentWindow;
            //             // childWindow.initDyData(1,data.data.longitude,data.data.latitude,val)
            //             //数据中心
            //             if(data.data.info.id){
            //                 document.getElementById("DCfrmmain").contentWindow.location.reload(true);
            //                 document.getElementById("DCfrmmain").src = "../Xmxx/DataCenter.html?projectId="+projectId+"&slkStyle=1";
            //             }
            //             //数据
            //             this.pushData(data.data.info);
            //             this.pushInfo(data.data.list)
            //             this.datacard = true
            //             this.DCcard = true
            //             this.wellcard = false
            //             this.infocard=true
            //             this.slkcard=false
            //
            //         } else {
            //             layer.msg(data.data);
            //         }
            //     }, (e) => {
            //         console.log(e);
            //     });
            // },
            //单元 地图点击事件
            gisDyClick: function (val, name) {
                this.rowid = val;
                this.infoName = name
                ajr_ajax_get("/slk/getSlkInfo", {projectId: val}, (data) => {
                    if (data.code == "0" || data.code == 0) {
                        // var childWindow = $("#frmmain")[0].contentWindow;
                        // childWindow.initDyData(1,data.data.longitude,data.data.latitude,val)
                        //数据中心
                        if (data.data.id) {
                            document.getElementById("DCfrmmain").contentWindow.location.reload(true);
                            document.getElementById("DCfrmmain").src = "DataCenter.html?projectId=" + projectId + "&slkStyle=1";
                        }
                        //数据
                        this.pushData(data.data);
                        // this.pushInfo(data.data.list)
                        this.datacard = true
                        this.DCcard = true
                        this.wellcard = false
                        this.infocard = true
                        this.slkcard = false
                        // $("#gisAndTable").hide();//隐藏div

                    } else {
                        layer.msg(data.data);
                    }
                }, (e) => {
                    console.log(e);
                });
            },
            //初始化地图
            initMap: function (row) {
                if (projectId) {
                    ajr_ajax_get("/slk/getSlkInfo", {projectId: projectId}, (data) => {
                        if (data.code == "0" || data.code == 0) {
                            //gis
                            this.url = "Gis/gis.html?id=" + projectId + "&longitude=" + data.data.longitude + "&latitude=" + data.data.latitude;

                            // var childWindow = $("#frmmain")[0].contentWindow;
                            // childWindow.initDyData(1,data.data.longitude,data.data.latitude,projectId)

                            //数据中心
                            if (data.data.id) {
                                document.getElementById("DCfrmmain").contentWindow.location.reload(true);
                                document.getElementById("DCfrmmain").src = "DataCenter.html?projectId=" + projectId + "&slkStyle=1";
                            }
                            //数据
                            this.pushData(data.data);

                            this.datacard = true
                            this.DCcard = true
                            // $("#gisAndTable").hide();//隐藏div

                        } else {
                            layer.msg(data.data);
                        }
                    }, (e) => {
                        console.log(e);
                    });
                } else {
                    this.url = "Gis/gis.html?longitude=" + this.longitude + "&latitude=" + this.latitude;
                }

            },
            //新建井位弹出
            openWells: function () {
                // this.content="确定（3s)"
                // this.centerDialogVisible=true
                // this.newSlk = true;
                // this.centerDialogVisible = true;
                // var interval = setInterval(() => {
                //     this.seconds--;
                //     this.content="确定（"+this.seconds+"s)"
                //     if (this.seconds <= 0) {
                //         this.centerDialogVisible = false;
                //         clearInterval(interval);
                //     }
                // }, 1000);
                // this.seconds = 3;
                this.newSlk = true;
                this.$message({
                    message: '请点击地图选择实例库位置',
                    type: 'success'
                });

            },
            openCgcj(){
                window.open("SlkcgHome.html");
            },
            allowToCast() {
                // var body=document.getElementById("frmmain")
                // body.style.cursor= "move"
                this.centerDialogVisible = false
            },
            //行点击
            openDetails: function (row) {
                this.rowid = row.id;
                var childWindow = $("#frmmain")[0].contentWindow;
                childWindow.initDyData(1, row.longitude, row.latitude, row.id)

                //数据中心
                if (row.id) {
                    document.getElementById("DCfrmmain").contentWindow.location.reload(true);
                    document.getElementById("DCfrmmain").src = "DataCenter.html?projectId=" + row.id + "&slkStyle=1";
                }

                this.pushData(row);
                this.wellcard = false
                this.datacard = true
                this.DCcard = true
                // $("#gisAndTable").hide();//隐藏div
            },
            //数据面板赋值
            pushData: function (val) {
                this.slkList = []

                var dzcl = "";
                if (val.dzcl) {
                    dzcl = val.dzcl + "(10^4t)"
                }

                var hymj = "";
                if (val.hymj) {
                    hymj = val.hymj + "(km^2)"
                }

                var dxyynd = "";
                if (val.dxyynd) {
                    dxyynd = val.dxyynd + "(mPa.s)"
                }

                var dmyynd = "";
                if (val.dmyynd) {
                    dmyynd = val.dmyynd + "(mPa.s)"
                }

                var pjkxd = "";
                if (val.pjkxd) {
                    pjkxd = val.pjkxd + "(%)"
                }

                var pjstl = "";
                if (val.pjstl) {
                    pjstl = val.pjstl + "(10^-3um^2)"
                }
                var yshybhd = "";
                if (val.yshybhd) {
                    yshybhd = val.yshybhd + "(%)"
                }

                this.slkList.push(
                    {name: "实例名称：", value: val.oilName},
                    {name: "单位：", value: val.xzdw},
                    {name: "时间：", value: val.sj},
                    {name: "油气藏类型：", value: val.yqclx},
                    {name: "地质储量：", value: dzcl},
                    {name: "含油面积：", value: hymj},
                    {name: "地下原油粘度：", value: dxyynd},
                    {name: "地面原油粘度：", value: dmyynd},
                    {name: "平均孔隙度：", value: pjkxd},
                    {name: "平均渗透率：", value: pjstl},
                    {name: "原始含油饱和度：", value: yshybhd},
                )
            },
            pushInfo: function (val) {
                this.infoList = []
                this.imgList = []

                val.forEach(item => {
                    if (item.kzm.indexOf("PNG") > -1 || item.kzm.indexOf("JPG") > -1 || item.kzm.indexOf("IMG") > -1) {
                        this.imgList.push(item)
                    } else if (item.lx === "jl") {
                        this.infoList.push(item)
                    }
                })
                if (this.imgList.length > 15) {
                    this.imgList = this.imgList.slice(0, 15)
                }
                if (this.infoList.length > 3) {
                    this.infoList = this.infoList.slice(0, 3)
                }
            },
            imgLogo: function (val) {
                this.imgUrl = val.content
                this.dialogFormVisibleImg = true
            },
            //关闭弹出面板
            stopGet: function () {
                this.slkcard = false;
            },
            //显示弹出面板
            showSlkcard: function () {
                this.slkcard = true;
                this.infocard = false;
                var childWindow = $("#frmmain")[0].contentWindow;
                childWindow.initDyData(2, this.longitude, this.latitude);
            },
            //获取表格数据
            getData: function () {
                ajr_ajax_get("/slk/getSlkList", {
                    name: this.name,
                    longitude: this.longitude,
                    latitude: this.latitude
                }, (data) => {
                    if (data.code == "0" || data.code == 0) {
                        // 设置数据
                        this.tableData = data.data
                    } else {
                        // alert(response.data.msg)
                        layer.msg(data.data);
                    }
                }, (e) => {
                    console.log(e);
                });
            },
            //查询按钮
            searchresultGis: function () {
                this.slkcard = true;
                this.getData();
            },
            //高级
            openSearchGis: function () {
                var _this = this;

                var index = layer.open({
                    type: 2,
                    skin: 'layui-layer-lan',
                    shade: false,
                    maxmin: true,
                    // scrollbar: false,
                    title: "井斜角",
                    area: ['690px', '508px'],
                    btn: ['关闭'],
                    content: ["SlkSearchBox.html", "yes"],
                    success: function (layero, index) {

                        // //获取子页面的元素，进行数据渲染
                        var ifrm = layero.find("iframe")[0].contentWindow;
                        ifrm.app.searchModel.xzdw = _this.xzdw
                        ifrm.app.searchModel.sj = _this.sj
                        ifrm.app.searchModel.yqclx = _this.yqclx
                        ifrm.app.searchModel.dzcl = _this.dzcl
                        ifrm.app.searchModel.hymj = _this.hymj
                        ifrm.app.searchModel.dxyynd = _this.dxyynd
                        ifrm.app.searchModel.dmyynd = _this.dmyynd
                        ifrm.app.searchModel.pjkxd = _this.pjkxd
                        ifrm.app.searchModel.pjstl = _this.pjstl
                        ifrm.app.searchModel.yshybhd = _this.yshybhd



                        ifrm.app.judgeModel.judge_xzdw = _this.judge_xzdw
                        ifrm.app.judgeModel.judge_sj = _this.judge_sj
                        ifrm.app.judgeModel.judge_yqclx = _this.judge_yqclx
                        ifrm.app.judgeModel.judge_dzcl = _this.judge_dzcl
                        ifrm.app.judgeModel.judge_hymj = _this.judge_hymj
                        ifrm.app.judgeModel.judge_dxyynd = _this.judge_dxyynd
                        ifrm.app.judgeModel.judge_dmyynd = _this.judge_dmyynd
                        ifrm.app.judgeModel.judge_pjkxd = _this.judge_pjkxd
                        ifrm.app.judgeModel.judge_pjstl = _this.judge_pjstl
                        ifrm.app.judgeModel.judge_yshybhd = _this.judge_yshybhd


                    },
                    yes: function (index, layero) {
                        var ifrm = layero.find("iframe")[0].contentWindow;

                        _this.xzdw = ifrm.app.searchModel.xzdw;
                        _this.sj = ifrm.app.searchModel.sj;
                        _this.yqclx = ifrm.app.searchModel.yqclx;
                        _this.dzcl = ifrm.app.searchModel.dzcl;
                        _this.hymj = ifrm.app.searchModel.hymj;
                        _this.dxyynd = ifrm.app.searchModel.dxyynd;
                        _this.dmyynd = ifrm.app.searchModel.dmyynd;
                        _this.pjkxd = ifrm.app.searchModel.pjkxd;
                        _this.pjstl = ifrm.app.searchModel.pjstl;
                        _this.yshybhd = ifrm.app.searchModel.yshybhd;


                        _this.judge_xzdw = ifrm.app.judgeModel.judge_xzdw;
                        _this.judge_sj = ifrm.app.judgeModel.judge_sj;
                        _this.judge_yqclx = ifrm.app.judgeModel.judge_yqclx;
                        _this.judge_dzcl = ifrm.app.judgeModel.judge_dzcl;
                        _this.judge_hymj = ifrm.app.judgeModel.judge_hymj;
                        _this.judge_dxyynd = ifrm.app.judgeModel.judge_dxyynd;
                        _this.judge_dmyynd = ifrm.app.judgeModel.judge_dmyynd;
                        _this.judge_pjkxd = ifrm.app.judgeModel.judge_pjkxd;
                        _this.judge_pjstl = ifrm.app.judgeModel.judge_pjstl;
                        _this.judge_yshybhd = ifrm.app.judgeModel.judge_yshybhd;


                        var sParams = {
                            name: _this.name,
                        };

                        //单位
                        if (_this.xzdw) {
                            Object.assign(sParams, {
                                xzdw: _this.xzdw
                            });
                        }
                        //时间
                        if (_this.judge_sj && _this.sj) {
                            Object.assign(sParams, {
                                sj: " and to_date(sj,'yyyy.mm.dd') " + _this.judge_sj + "to_date(" + _this.sj + ",'yyyy.mm.dd')"
                            });
                        }
                        //油气藏类型
                        if (_this.yqclx) {
                            Object.assign(sParams, {
                                yqclx: _this.yqclx
                            });
                        }
                        //含油面积(Km²)
                        if (_this.judge_hymj && _this.hymj) {
                            Object.assign(sParams, {
                                hymj: " and hymj " + _this.judge_hymj + _this.hymj
                            });
                        }
                        //地质储量(10⁴t)/(10⁸m³)
                        if (_this.judge_dzcl && _this.dzcl) {
                            Object.assign(sParams, {
                                dzcl: " and dzcl " + _this.judge_dzcl + _this.dzcl
                            });
                        }
                        //地下原油粘度
                        if (_this.judge_dxyynd && _this.dxyynd) {
                            Object.assign(sParams, {
                                dxyynd: " and dxyynd " + _this.judge_dxyynd + _this.dxyynd
                            });
                        }
                        //地面原油粘度
                        if (_this.judge_dmyynd && _this.dmyynd) {
                            Object.assign(sParams, {
                                dmyynd: " and dmyynd " + _this.judge_dmyynd + _this.dmyynd
                            });
                        }
                        //平均孔隙度
                        if (_this.judge_pjkxd && _this.pjkxd) {
                            Object.assign(sParams, {
                                pjkxd: " and pjkxd " + _this.judge_pjkxd + _this.pjkxd
                            });
                        }
                        //平均渗透率
                        if (_this.judge_pjstl && _this.pjstl) {
                            Object.assign(sParams, {
                                pjstl: " and pjstl " + _this.judge_pjstl + _this.pjstl
                            });
                        }
                        //原始含油饱和度
                        if (_this.judge_yshybhd && _this.yshybhd) {
                            Object.assign(sParams, {
                                yshybhd: " and yshybhd " + _this.judge_yshybhd + _this.yshybhd
                            });
                        }

                        ajr_ajax_get("/slk/getSlkList", sParams, (data) => {
                            if (data.code == "0" || data.code == 0) {
                                // 设置数据
                                _this.tableData = data.data
                                layer.close(index);

                                _this.datacard = false
                                _this.DCcard = false
                                _this.wellcard = false
                                _this.infocard = false
                                _this.slkcard = true
                                // $("#gisAndTable").show();//显示div

                            } else {
                                layer.msg(data.data);
                            }
                        }, (e) => {

                            console.log(e);
                        });
                    },
                    btn2:function(index,layero){
                        _this.resetSearch(index,layero)
                        return false;
                    }
                });
            },
            resetSearch:function(index, layero){
                var ifrm = layero.find("iframe")[0].contentWindow;

                ifrm.app.searchModel.xzdw = '',
                ifrm.app.searchModel.sj = '',
                ifrm.app.searchModel.yqclx = '',
                ifrm.app.searchModel.dzcl = '',
                ifrm.app.searchModel.hymj = '',
                ifrm.app.searchModel.dxyynd = '',
                ifrm.app.searchModel.dmyynd = '',
                ifrm.app.searchModel.pjkxd = '',
                ifrm.app.searchModel.pjstl = '',
                ifrm.app.searchModel.yshybhd = '',

                ifrm.app.searchModel.judge_xzdw = '',
                ifrm.app.searchModel.judge_sj = '',
                ifrm.app.searchModel.judge_yqclx = '',
                ifrm.app.searchModel.judge_dzcl = '',
                ifrm.app.searchModel.judge_hymj = '',
                ifrm.app.searchModel.judge_dxyynd = '',
                ifrm.app.searchModel.judge_dmyynd = '',
                ifrm.app.searchModel.judge_pjkxd = '',
                ifrm.app.searchModel.judge_pjstl = '',
                ifrm.app.searchModel.judge_yshybhd = ''

            },
            //点击事件，跳转实例页面
            rowClickGis: function (val) {
                if (val) {
                    window.open("SlkContentView.html?projectId=" + val.id);
                } else {
                    window.open("SlkContentView.html?projectId=" + this.rowid);
                }
            },
            infoClick: function () {
                this.infocard = false
            },
            openSLK: function () {
                window.open("SlkList.html");
            },
            tableRowClassName(row, index) {
                return 'tabelhover';
            },
            changePage: function (event) {
                this.pageNo = event;
                this.getData();
            },
            //获取表格数据
            getData: function () {
                ajr_ajax_get("/slk/getSlkList", {name: this.name, page: this.pageNo, rows: this.pageSize}, (data) => {
                    if (data.code == "0" || data.code == 0) {
                        // 设置数据
                        this.tableData = data.data
                    } else {
                        // alert(response.data.msg)
                        layer.msg(data.msg);
                    }
                }, (e) => {
                    console.log(e);
                });
            },
            searchresult: function () {
                this.getData();
            },
            openSearch: function () {
                // this.title = "高级筛选"
                //
                // this.searchFormVisible = true
                var _this = this;

                var index = layer.open({
                    type: 2,
                    skin: 'layui-layer-lan',
                    shade: false,
                    maxmin: true,
                    // scrollbar: false,
                    title: "筛选",
                    area: ['1120px', '658px'],
                    btn: ['确定','重置', '关闭'],
                    content: ["SlkSearchBox.html", "yes"],

                    success: function (layero, index) {
                        // //获取子页面的元素，进行数据渲染
                        var ifrm = layero.find("iframe")[0].contentWindow;
                        ifrm.app.searchModel.xzdw = _this.xzdw
                        ifrm.app.searchModel.sj = _this.sj
                        ifrm.app.searchModel.yqclx = _this.yqclx
                        ifrm.app.searchModel.dzcl = _this.dzcl
                        ifrm.app.searchModel.hymj = _this.hymj
                        ifrm.app.searchModel.dxyynd = _this.dxyynd
                        ifrm.app.searchModel.dmyynd = _this.dmyynd
                        ifrm.app.searchModel.pjkxd = _this.pjkxd
                        ifrm.app.searchModel.pjstl = _this.pjstl
                        ifrm.app.searchModel.yshybhd = _this.yshybhd

                        ifrm.app.searchModel.judge_xzdw = _this.judge_xzdw
                        ifrm.app.searchModel.judge_sj = _this.judge_sj
                        ifrm.app.searchModel.judge_yqclx = _this.judge_yqclx
                        ifrm.app.searchModel.judge_dzcl = _this.judge_dzcl
                        ifrm.app.searchModel.judge_hymj = _this.judge_hymj
                        ifrm.app.searchModel.judge_dxyynd = _this.judge_dxyynd
                        ifrm.app.searchModel.judge_dmyynd = _this.judge_dmyynd
                        ifrm.app.searchModel.judge_pjkxd = _this.judge_pjkxd
                        ifrm.app.searchModel.judge_pjstl = _this.judge_pjstl
                        ifrm.app.searchModel.judge_yshybhd = _this.judge_yshybhd
                    },
                    yes: function (index, layero) {
                        var ifrm = layero.find("iframe")[0].contentWindow;

                        _this.xzdw = ifrm.app.searchModel.xzdw;
                        _this.sj = ifrm.app.searchModel.sj;
                        _this.yqclx = ifrm.app.searchModel.yqclx;
                        _this.dzcl = ifrm.app.searchModel.dzcl;
                        _this.hymj = ifrm.app.searchModel.hymj;
                        _this.dxyynd = ifrm.app.searchModel.dxyynd;
                        _this.dmyynd = ifrm.app.searchModel.dmyynd;
                        _this.pjkxd = ifrm.app.searchModel.pjkxd;
                        _this.pjstl = ifrm.app.searchModel.pjstl;
                        _this.yshybhd = ifrm.app.searchModel.yshybhd;


                        _this.judge_xzdw = ifrm.app.judgeModel.judge_xzdw;
                        _this.judge_sj = ifrm.app.judgeModel.judge_sj;
                        _this.judge_yqclx = ifrm.app.judgeModel.judge_yqclx;
                        _this.judge_dzcl = ifrm.app.judgeModel.judge_dzcl;
                        _this.judge_hymj = ifrm.app.judgeModel.judge_hymj;
                        _this.judge_dxyynd = ifrm.app.judgeModel.judge_dxyynd;
                        _this.judge_dmyynd = ifrm.app.judgeModel.judge_dmyynd;
                        _this.judge_pjkxd = ifrm.app.judgeModel.judge_pjkxd;
                        _this.judge_pjstl = ifrm.app.judgeModel.judge_pjstl;
                        _this.judge_yshybhd = ifrm.app.judgeModel.judge_yshybhd;


                        var sParams = {
                            name: _this.name,
                        };

                        //单位
                        if (_this.xzdw) {
                            Object.assign(sParams, {
                                xzdw: _this.xzdw
                            });
                        }
                        //时间
                        if (_this.judge_sj && _this.sj) {
                            Object.assign(sParams, {
                                sj: " and to_date(sj,'yyyy.mm.dd') " + _this.judge_sj + "to_date(" + _this.sj + ",'yyyy.mm.dd')"
                            });
                        }
                        //油气藏类型
                        if (_this.yqclx) {
                            Object.assign(sParams, {
                                yqclx: _this.yqclx
                            });
                        }
                        //含油面积(Km²)
                        if (_this.judge_hymj && _this.hymj) {
                            Object.assign(sParams, {
                                hymj: " and hymj " + _this.judge_hymj + _this.hymj
                            });
                        }
                        //地质储量(10⁴t)/(10⁸m³)
                        if (_this.judge_dzcl && _this.dzcl) {
                            Object.assign(sParams, {
                                dzcl: " and dzcl " + _this.judge_dzcl + _this.dzcl
                            });
                        }
                        //地下原油粘度
                        if (_this.judge_dxyynd && _this.dxyynd) {
                            Object.assign(sParams, {
                                dxyynd: " and dxyynd " + _this.judge_dxyynd + _this.dxyynd
                            });
                        }
                        //地面原油粘度
                        if (_this.judge_dmyynd && _this.dmyynd) {
                            Object.assign(sParams, {
                                dmyynd: " and dmyynd " + _this.judge_dmyynd + _this.dmyynd
                            });
                        }
                        //平均孔隙度
                        if (_this.judge_pjkxd && _this.pjkxd) {
                            Object.assign(sParams, {
                                pjkxd: " and pjkxd " + _this.judge_pjkxd + _this.pjkxd
                            });
                        }
                        //平均渗透率
                        if (_this.judge_pjstl && _this.pjstl) {
                            Object.assign(sParams, {
                                pjstl: " and pjstl " + _this.judge_pjstl + _this.pjstl
                            });
                        }
                        //原始含油饱和度
                        if (_this.judge_yshybhd && _this.yshybhd) {
                            Object.assign(sParams, {
                                yshybhd: " and yshybhd " + _this.judge_yshybhd + _this.yshybhd
                            });
                        }

                        ajr_ajax_get("/slk/getSlkList", sParams, (data) => {
                            if (data.code == "0" || data.code == 0) {
                                // 设置数据
                                _this.tableData = data.data
                                layer.close(index);

                                _this.slkcard = true;
                            } else {
                                layer.msg(data.data);
                            }
                        }, (e) => {

                            console.log(e);
                        });
                    },
                    btn2:function(index,layero){
                        _this.resetSearch(index,layero)
                        return false;
                    }
                });
            },
            closeDialog: function () {
                var sParams = {
                    name: this.name,
                    page: this.pageNo,
                    rows: this.pageSize
                };
                if (this.searchModel.yqclx) {
                    Object.assign(sParams, {
                        yqclx: this.searchModel.yqclx
                    });
                }
                if (this.judge18 && this.searchModel.hymj) {
                    Object.assign(sParams, {
                        judge18: this.judge18
                    });
                    Object.assign(sParams, {
                        hymj: this.searchModel.hymj
                    });
                }
                if (this.judge20 && this.searchModel.dzcl) {
                    Object.assign(sParams, {
                        judge20: this.judge18
                    });
                    Object.assign(sParams, {
                        dzcl: this.searchModel.dzcl
                    });
                }
                ajr_ajax_get("/slk/getSlkList", sParams, (data) => {
                    if (data.code == "0" || data.code == 0) {
                        this.total = data.data.total
                        // 设置数据
                        this.tableData = data.data
                    } else {
                        // alert(response.data.msg)
                        layer.msg(data.msg);
                    }
                }, (e) => {
                    console.log(e);
                });
                this.searchFormVisible = false
            },
            getSearchDictArray: function () {

                ajr_ajax_get("/ajrDatatableFieldDic/getDicList", {tableCode: 'SLK_YQCLX'}, (data) => {
                    if (data.code == "0" || data.code == 0) {
                        this.settleSearchCompanyArray(data.data);
                    } else {
                        // alert(response.data.msg)
                        layer.msg(data.msg);
                    }
                }, (e) => {
                    console.log(e);
                });


            },
            //整理下拉列表数据
            settleSearchCompanyArray: function (array) {
                for (let i = 0; i < array.length; i++) {
                    this.searchDictArray.push({
                        label: array[i].fieldName,
                        value: array[i].fieldName
                    })
                }
            },
            rowClick: function (val) {
                window.open("SlkContentView.html?projectId=" + val.id);
            },
            //重置
            clearSearch: function () {
                this.judge3 = '', this.judge4 = '', this.judge5 = '', this.judge6 = '', this.judge7 = '', this.judge8 = '', this.judge9 = '',
                    this.judge10 = '', this.judge11 = '', this.judge12 = '', this.judge13 = '', this.judge14 = '', this.judge15 = '', this.judge16 = '', this.judge17 = '', this.judge18 = '',
                    this.judge19 = '', this.judge20 = '', this.judge21 = '', this.judge22 = '', this.judge23 = '', this.judge24 = '', this.judge25 = '', this.judge26 = '', this.judge27 = '',
                    this.judge28 = '', this.judge29 = '', this.judge30 = '', this.judge31 = '', this.judge32 = '', this.judge33 = '', this.judge34 = '', this.judge35 = '', this.judge36 = ''
                this.searchModel.yqclx = null,
                    this.searchModel.dzcl = null,
                    this.searchModel.hymj = null,
                    this.searchModel.dycsny = null,
                    this.searchModel.zsny = null,
                    this.searchModel.zqny = null,
                    this.searchModel.hsny = null,
                    this.searchModel.kfcx = null,
                    this.searchModel.ysdcyl = null,
                    this.searchModel.dcyc = null,
                    this.searchModel.jwmd = null,
                    this.searchModel.sqcl = null,
                    this.searchModel.rccl = null,
                    this.searchModel.rjqkccl = null,
                    this.searchModel.sqkzcl = null,
                    this.searchModel.bdcsl = null,
                    this.searchModel.qhsxs = null,
                    this.searchModel.bdrcyl = null,
                    this.searchModel.bdrcql = null,
                    this.searchModel.dtzcdyl = null,
                    this.searchModel.yckcfs = null,
                    this.searchModel.kfny = null,
                    this.searchModel.zqny1 = null,
                    this.searchModel.zqny2 = null,
                    this.searchModel.qqny = null,
                    this.searchModel.trkfny = null,
                    this.searchModel.nd = null,
                    this.searchModel.mqdcy = null,
                    this.searchModel.qqcl = null,
                    this.searchModel.rjqdzcl = null,
                    this.searchModel.kccl = null,
                    this.searchModel.sqdycl = null,
                    this.searchModel.yhsxs = null,
                    this.searchModel.sykccl = null,
                    this.searchModel.bdhsrcyl = null,
                    this.searchModel.jtzcdyl = null

            }

        }
    
    });
</script>

