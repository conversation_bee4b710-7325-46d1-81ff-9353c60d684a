
//+++++++++++++++++++++++++++++++++

function getServerIP(){
    var urlPath = window.document.location.href;
    var docPath = window.document.location.pathname;
    var index = urlPath.indexOf(docPath);
    var serverIp = urlPath.substring(0, index);
    if (serverIp.indexOf(':') > -1) {
        var ips = serverIp.split(':');
        if(ips.length>1){
            serverIp=ips[1];
            if(serverIp.indexOf('\/\/')>-1){
                serverIp=serverIp.substring(2);
            }
        }
    }
    return serverIp;
}

//+++++++++++++++++++++++++++++++++

//var VS_ENV = "dev";
var VS_ENV = "prod";

// var serverapi = "http://**************:8888";
// var serverapi = "http://**************:8888";
// var staticServerUrl = "http://**************/static/alllink/";
//var serverapi = "http://127.0.0.1:8888";

var _serverIp = getServerIP() ;

var serverapi = "http://"+ _serverIp +":8085/dataApi";
var staticServerUrl = "http://" +_serverIp+ ":8085/static/alllink/";
var mapServer = "http://"+ _serverIp +":8085/mapxx";

if(_serverIp.indexOf("192.168")>-1
    || _serverIp.indexOf("localhost")>-1
    || _serverIp.indexOf("127.0.0.1")>-1){
    serverapi = "http://"+ _serverIp +":8888";
    staticServerUrl = "http://" +_serverIp+ "/static/alllink/";
}

var serverPath = "/static/alllink/";

var serverPath1 = "";

if( isNull(_serverIp) ) {
    _serverIp = '************';
    serverapi = 'http://' + _serverIp + ':8888';
    staticServerUrl = 'http://' + _serverIp + ':8085/static/alllink/';
    serverPath = staticServerUrl;
    serverPath1 = "http://" + _serverIp + ":8085/";
    mapServer = "http://"+ _serverIp +":8085/mapxx";
}

var userid = 'chengrui';

if (VS_ENV == "dev")
    serverapi = "http://localhost:30364";

//if (document.cookie.indexOf("Authorization=")==-1)
//{
//    location.href = "/html/home/<USER>";
//  location.href = "/html/home/<USER>";
//}

function getServerPath() {
    // debugger
    var urlPath = window.document.location.href;
    var docPath = window.document.location.pathname;
    var index = urlPath.indexOf(docPath);
    var serverTemp = urlPath.substring(0, index);
    console.log(serverTemp);
}

//getServerPath();

function BasePost(args,type,url,callback,err) {
    $.ajax({
        //请求方式
        type: "POST",
        //请求的媒体类型
        contentType: "application/x-www-form-urlencoded;charset=UTF-8",
        //请求地址
        url: serverapi + url,
        //数据，json字符串
        data: args,
        beforeSend: function (xhr) {
            // 发送ajax请求之前向http的head里面加入验证信息
            xhr.setRequestHeader("Authorization", window.localStorage.getItem("Authorization"));
            // 请求发起前在头部附加token
        },
        //请求成功
        success: function (result) {
            callback && callback(result);
        },
        //请求失败，包含具体的错误信息
        error: function (e) {
            err && err(e.status);
        },
        error:function(e){
            err && err(e);
        },
    });
}

var ajr_ajax_get = function (url, args, callback, err) {

    if (VS_ENV == "prod") {

        $.get(serverapi + url, args, (data) => {
            let ret = data ; //eval('('+data+')');//JSON.parse(data);
            if(typeof data === 'string'){
                ret = eval('('+data+')');
            }
            if( ret!=null){
                if(ret.code=="9"){
                    location.href="../../html/login.html";
                    return ;
                }
                if(ret.code != null && ret.code=="-1"){
                    location.href="../../html/login.html";
                    return ;
                }
                callback && callback(data);
            }
        }).error((e) => {
            err && err(e);
        });
    }
    else {
        if (callback != null) {
            callback(args);
        }
    }
}

var ajr_ajax_post = function (url, args, callback, err) {

    if (VS_ENV == "prod") {

        $.post(serverapi + url, args, (data) => {

            let ret = data ; //eval('('+data+')');//JSON.parse(data);
            if(typeof data === 'string'){
                ret = eval('('+data+')');
            }

            if( ret!=null){
                if(ret.code=="9"){
                    location.href="../../html/login.html";
                    return ;
                }
                if(ret.code != null && ret.code=="-1"){
                    location.href="../../html/login.html";
                    return ;
                }
                if (callback != null) {
                    callback(data);
                }
            }
        }).error((e) => {
            if (err != null) {
                err(e);
            }
        });
    }
    else {
        if (callback != null) {
            callback(args);
        }
    }
}

var ajr_ajax_post1 = function (url, args, callback, err) {

    if (VS_ENV == "prod") {

        jQuery.post(url, args, (data) => {
            if (callback != null) {
                callback(data);
            }
        }).error((e) => {
            if (err != null) {
                err(e);
            }
        });
    }
    else {
        if (callback != null) {
            callback(args);
        }
    }
}

var ajr_ajax_get1 = function (url, args, callback, err) {
    if (VS_ENV == "prod") {
        jQuery.get(url, args, (data) => {
            if (callback != null) {
                callback(data);
            }
        }).error((e) => {
            if (err != null) {
                err(e);
            }
        });
    }
    else {
        if (callback != null) {
            callback(args);
        }
    }
}

var ajr_ajax_post_json = function (option) {
    option.url = serverapi + option.url;
    if (option.type == "get") {
        //ajr_ajax_get(option.url, option.data, option.success, option.error);
        $.get(option.url, option.data, (data) => {
            if (option.success != null) {
                //console.log(data);
                if(typeof data === 'string' && data != ""){
                    data = eval('('+data+')');
                }
                if( data!=null){
                    if(data.code != null && (data.code == "9" || data.code == 9) ){
                        location.href="../../html/login.html";
                        return ;
                    }
                    if (data.code == "0" || data.code == 0) {
                        option.success(data.data);
                    } else {
                        if (option.error != null) {
                            option.error(data.msg);
                        }
                    }
                }
            }
        },'').error((e) => {
            if (option.error != null) {
                option.error(e);
            }
        });
    } else if (option.type == "post") {
        $.post(option.url, option.data, (data) => {
            if (option.success != null) {
                if(typeof data === 'string' && data != ""){
                    data = eval('('+data+')');
                }
                if( data!=null){
                    if(data.code != null && (data.code == "9" || data.code == 9)){
                        location.href="../../html/login.html";
                        return ;
                    }
                    if (data.code == "0" || data.code == 0) {
                        option.success(data.data);
                    } else {
                        if (option.error != null) {
                            option.error(data.msg);
                        }
                    }
                }
            }
        }).error((e) => {
            if (option.error != null) {
                option.error(e);
            }
        });
    }
    //$.ajax(option);
}

var ajr_ajax_post_json_gai = function (option) {
    option.url = serverapi + option.url;

    $.ajax({
        //请求方式
        type: "POST",
        //请求的媒体类型
        contentType: "application/json",
        //请求地址
        url: option.url,
        //数据，json字符串
        data: option.data,
        beforeSend: function (xhr) {
            //发送ajax请求之前向http的head里面加入验证信息
            xhr.setRequestHeader("Authorization", window.localStorage.getItem("Authorization"));  // 请求发起前在头部附加token
        },
        //请求成功
        success: function (data) {
            if (option.success != null) {
                if(typeof data === 'string' && data != ""){
                    data = eval('('+data+')');
                }
                if( data!=null){
                    if(data.code != null && (data.code == "9" || data.code == 9)){
                        location.href="../../html/login.html";
                        return ;
                    }
                    if (data.code == "0" || data.code == 0) {
                        option.success(data.data);
                    } else {
                        if (option.error != null) {
                            option.error(data.msg);
                        }
                    }
                }
            }
        },
        //请求失败，包含具体的错误信息
        error: function (e) {
            if (option.error != null) {
                option.error(e);
            }
        },
    });
    /*
    $.post(option.url, option.data, (data) => {

    }).error((e) => {
        if (option.error != null) {
            option.error(e);
        }
    });*/
}

var ajr_ajax_form = function (url, frm, callback) {

    if (VS_ENV == "prod") {

        let __tkn = "Authorization";
        let __utk = "";
        if (url.indexOf("/sys/login") == -1) {
            __utk = window.localStorage.getItem(__tkn);
        }

        let config = {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': __utk ,
            }
        };

        let req = new XMLHttpRequest();

        req.onreadystatechange = function () {

            if (req.readyState == 4
                && req.status == 200) {

                if (callback != null) {
                    if (req != null && req.responseText.length > 0) {
                        //console.log("req.responseText:", req.responseText, eval("(" + req.responseText + ")"));
                        let rs = eval("(" + req.responseText + ")");
                        if (rs != null) {
                            if(rs.data!=null){
                                callback(rs.data);
                            }else{
                                callback(rs);
                            }
                        }
                    }
                }
            }
        }

        req.open("post", serverapi + url, false);

        req.setRequestHeader('Authorization', __utk);

        req.send(frm);

    }
    else {
        if (callback != null) {
            callback({});
        }
    }
};

var ajr_ajax_formUp = function (url, frm, callback) {

    if (VS_ENV == "prod") {

        let __tkn = "Authorization";
        let __utk = "";
        if (url.indexOf("/sys/login") == -1) {
            __utk = window.localStorage.getItem(__tkn);
        }

        let config = {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': __utk ,
            }
        };

        let req = new XMLHttpRequest();

        req.onreadystatechange = function () {

            if (req.readyState == 4
                && req.status == 200) {

                if (callback != null) {
                    if (req != null && req.responseText.length > 0) {
                        //console.log("req.responseText:", req.responseText, eval("(" + req.responseText + ")"));
                        let rs = eval("(" + req.responseText + ")");
                        if (rs != null) {
                            if(rs.data!=null){
                                callback(rs);
                            }else{
                                callback(rs);
                            }
                        }
                    }
                }
            }
        }

        req.open("post", serverapi + url, true);

        req.setRequestHeader('Authorization', __utk);

        req.send(frm);

    }
    else {
        if (callback != null) {
            callback({});
        }
    }
};

var ajr_ajax_form1 = function (url, frm, callback) {

    if (VS_ENV == "prod") {

        let config = {
            headers: {
                'Content-Type': 'multipart/form-data',
            }
        };

        let req = new XMLHttpRequest();
        req.onreadystatechange = function () {

            if (req.readyState == 4
                && req.status == 200) {

                if (callback != null) {
                    if (req != null && req.responseText.length > 0) {
                        //console.log("req.responseText:", req.responseText, eval("(" + req.responseText + ")"));
                        let rs = eval("(" + req.responseText + ")");
                        if (rs != null && rs.data != null) {
                            callback(rs.data);
                        }
                    }
                }
            }
        }

        req.open("post", url, false);
        req.send(frm);
    }
    else {
        if (callback != null) {
            callback({});
        }
    }
};

var ajrSysLog = function(ms,bz){
    ajr_ajax_post("/ajrSysLog/log",{ms:ms,bz:bz},(data)=>{
        //console.log(data);
    },(e)=>{
        //console.log("");
    });
};

var headerHeight = 60 ;

function setContainerHeight(objid, scaleOpt) {

    if (objid == null)
        return 0;

    var scaleWidth = parseInt($("#" + objid).width());

    if (scaleWidth == null
        || parseInt(scaleWidth) == 'NaN'
        || parseInt(scaleWidth) < 1)
        return 0;

    if (scaleOpt == null)
        return 0;

    if (scaleOpt.width == null
        || parseInt(scaleOpt.width) == 'NaN'
        || parseInt(scaleOpt.width) < 1
        || scaleOpt.height == null
        || parseInt(scaleOpt.height) == 'NaN'
        || parseInt(scaleOpt.height) < 1)
        return 0;

    var wd = parseInt(scaleOpt.width);
    var ht = parseInt(scaleOpt.height);

    //var w = $(window).width();

    var h = scaleWidth * ht / wd;

    $("#" + objid).css("height", parseInt(h));

    return parseInt(h);

}

// scaleWidth = 18 || 丁文阳
// scaleOpt = { width:16,height:9 }
function getScaleHeight(scaleWidth, scaleOpt) {

    if (scaleWidth == null
        || parseInt(scaleWidth) == 'NaN'
        || parseInt(scaleWidth) < 1)
        return 0;

    scaleWidth = parseInt(scaleWidth);

    if (scaleOpt == null)
        return 0;

    if (scaleOpt.width == null
        || parseInt(scaleOpt.width) == 'NaN'
        || parseInt(scaleOpt.width) < 1
        || scaleOpt.height == null
        || parseInt(scaleOpt.height) == 'NaN'
        || parseInt(scaleOpt.height) < 1)
        return 0;

    var wd = parseInt(scaleOpt.width);
    var ht = parseInt(scaleOpt.height);

    var w = $(window).width();
    var h = w * scaleWidth / 24 * ht / wd;

    return parseInt(h);

}

var Common = {
    SetContainerHeight: setContainerHeight,
    GetScaleHeight: getScaleHeight,
};

//返回url的参数部分
function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null)
        return decodeURIComponent(r[2]);
    return null;
}

function RequestUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null)
        return decodeURIComponent(r[2]);
    return null;
}

function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
}

function createguid() {
    return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
}

function openWindow(url, data) {
    url += '?';
    for (var key in data) {
        url += '&' + key + '=' + data[key]
    }
    return window.open(url, "_blank");
}

function isNull(obj) {
    return obj == null || obj == 'null' || obj == '' || obj == undefined || obj == 'undefined';
}


function getsec(str) {

    var str1 = str.substring(1, str.length) * 1;
    var str2 = str.substring(0, 1);
    if (str2 == "s") {
        return str1 * 1000;
    }
    else if (str2 == "h") {
        return str1 * 60 * 60 * 1000;
    }
    else if (str2 == "d") {
        return str1 * 24 * 60 * 60 * 1000;
    }
}

function __getCookie(name) {

    return window.localStorage.getItem(name);

    var strCookie = document.cookie;
    var arrCookie = strCookie.split("; ");
    for (var i = 0; i < arrCookie.length; i++) {
        var arr = arrCookie[i].split("=");
        if (arr[0] == name) return arr[1];
    }
    return "chengrui";
}


function __setCookie(name, value) {

    window.localStorage.setItem(name,value);

    return ;

    var Days = 30;
    var exp = new Date();
    exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
    var temp   = name + "=" + escape(value) + "; expires=" + exp.toGMTString()+";";
    document.cookie = temp;

    /*
    //这是有设定过期时间的使用示例：
    //s20是代表20秒
    //h是指小时，如12小时则是：h12
    //d是天数，30天则：d30
    */

    var time = "d30";

    var strsec = getsec(time);
    var exp = new Date();
    exp.setTime(exp.getTime() + strsec * 1);
    document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString();

}

function __delCookie(name) {
    window.localStorage.removeItem(name);
    return ;
    //var date = new Date();
    //date.setTime(date.getTime() - 10000);
    //document.cookie = name + "=a; expires=" + date.toGMTString();
}

function logout() {
    __delCookie("Authorization");
    __delCookie("Permission");
    location.href = "../../html/login.html";
}

function getUrlParams ( name ) {

    var url = window.location.search;
    if (url.indexOf('?') == 1) { return false; }

    url = url.substr(1);
    url = url.split('&');

    var name = name || '';
    var nameres;

    // 获取全部参数及其值
    for( var i = 0 ; i < url.length ; i++ ) {
        var info = url[i].split('=');
        var obj = {};
        obj[info[0]] = decodeURI(info[1]);
        url[i] = obj;
    }

    if (name) {
        for(var i=0;i<url.length;i++) {
            for (const key in url[i]) {
                if (key == name) {
                    nameres = url[i][key];
                }
            }
        }
    }
    else {
        nameres = url;
    }

    // 返回结果
    return nameres;

}

function printTime () {
    var timestamp = new Date().getTime();
    var date = new Date();
    date.setTime(timestamp);
    //console.log('时间:' , date);
}

function OpenBox() {
    var data = {};
    data.height =  window.innerHeight;
    return data;
}

function dragFunc(id, func) {
    var Drag = document.getElementById(id);
    Drag.onmousedown = function(event) {
        var ev = event || window.event;
        event.stopPropagation();
        var disX = ev.clientX - Drag.offsetLeft;
        var disY = ev.clientY - Drag.offsetTop;
        document.onmousemove = function(event) {
            var ev = event || window.event;
            Drag.style.left = ev.clientX - disX + "px";
            Drag.style.top = ev.clientY - disY + "px";
            Drag.style.cursor = "move";
            func && func();
        };
    };
    Drag.onmouseup = function() {
        document.onmousemove = null;
        this.style.cursor = "default";
    };
};

Date.prototype.Format = function (fmt) { //author: meizz
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}

function DateToStr(date) {
     var year = date.getFullYear();//年
     var month = date.getMonth();//月
     var day = date.getDate();//日
     var hours = date.getHours();//时
     var min = date.getMinutes();//分
     var second = date.getSeconds();//秒
     return year + "-" +
         ((month + 1) > 9 ? (month + 1) : "0" + (month + 1)) + "-" +
         (day > 9 ? day : ("0" + day)) + " " +
         (hours > 9 ? hours : ("0" + hours)) + ":" +
         (min > 9 ? min : ("0" + min)) + ":" +
         (second > 9 ? second : ("0" + second));
}

function getClassName (obj) {
    if (obj && obj.constructor && obj.constructor.toString()) {
        if(obj.constructor.name) {
            return obj.constructor.name;
        }
        let str = obj.constructor.toString();
        let arr;
        if(str.charAt(0) == '[') {
            arr = str.match(/\w+\s∗(\w+)\w+\s∗(\w+)/);
        } else {
            arr = str.match(/function\s*(\w+)/);
        }
        if (arr && arr.length == 2) {
            return arr[1];
        }
    }
    return undefined;
}

function launchIntoFullscreen(container) {
    var element = null;
    if(isNull(container)) {
        element = document.documentElement;
    } else {
        element = document.getElementById(container);
    }

    if(element.requestFullscreen){
        element.requestFullscreen();
    }
    else if(element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
    }
    else if(element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
    }
    else if(element.msRequestFullscreen) {
        element.msRequestFullscreen();
    }
}

function exitFullscreen() {
    if(document.exitFullscreen) {
        document.exitFullscreen();
    } else if(document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
    } else if(document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
    }
}

var pagePermission = null;
var permissionList = [];

function getPagePermission(perjectid, pageid, autoclose) {
    return true;
    var key = "Permission";
    if(!isNull(perjectid)) {
        var userid = __getCookie("userId");
        key = perjectid + '.Permission.' + userid;
    }
    var permission = __getCookie(key);
    var ret = true;
    if(isNull(permission)) {
        ret = false;
    } else {
        var v = permission == "-1" ? 1 : 0;
        for(var i = 0; i < 1000; i ++) {
            permissionList.push(v);
        }
        var list = permission.split(',');
        if(list.length > 0) {
            for(var i = 0; i < list.length; i ++) {
                var temp = list[i];
                if( temp.indexOf(pageid) == 0 ) {
                    temp = temp.slice(pageid.length);
                    temp = Number(temp);
                    if(temp > -1 && temp < 1000) {
                        permissionList[temp] = 1;
                    }
                }
            }
        }
        pagePermission = pageid;
        ret = permissionList[0] != 0;
    }
    if(autoclose && !ret) {
        if(parent != window) {

        } else {
            window.opener = null;
            window.open('', '_self');
            window.close();
        }
    }
    return ret;
}

function getPermission(id) {
    return true;

    if(permissionList.length > 0 && id.indexOf(pagePermission) == 0) {
        var temp = id.slice(pagePermission.length);
        temp = Number(temp);
        if(temp > -1 && temp < 1000) {
            return permissionList[temp] != 0;
        }
    }
    return false;
}

function getProductPermission(perjectid, callback) {
    callback(true, "");return;

    ajr_ajax_post_json({
        type:"post",
        url: "/xmxx/getProductPermission",
        data:{
            xmid: perjectid
        },
        success:function (res) {
            if(isNull(res)) {
                callback && callback(false, "权限不足");
            } else {
                var userid = __getCookie("userId");
                var key = perjectid + '.Permission.' + userid;
                __setCookie(key, res);
                callback && callback(true, "");
            }
        },
        error: function (e) {
            callback && callback(false, "网络错误");
        }
    })
}

if (typeof Vue != "undefined") {
    Vue.prototype.checkPermission = function(id) {
        return getPermission(id);
    }
}
