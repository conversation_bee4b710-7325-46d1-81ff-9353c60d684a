<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title></title>
    <link href="js/ol/ol.css" rel="stylesheet"/>
    <link href="js/ol/ol-ext.css">
    <link href="js/ol/box.css">
    <link rel="stylesheet" href="js/ol-ext.css" type="text/css">


    <script src="js/ol/ol.js"></script>
    <script src="js/ol-ext.js"></script>
    <script src="js/ol/ol-ext.js"></script>
    <script src="js/searchbox.js"></script>

    <script src="../../../content/js/jquery-1.10.2.js"></script>
    <script src="../../../content/js/Cookie.js"></script>
    <script src="../../../content/js/common.js"></script>
    <script src="js/proj4.js"></script>
    <script src="js/proj4_def.js"></script>
    <script src="../../../content/js/project/threadpool.min.js"></script>

    <script src="../../../content/js/mapStyle.js"></script>

    <!--    <script src="../../../content/map/proj4.js"></script>-->
    <!--    <script src="../../../content/map/proj4_def.js"></script>-->
    <script src="../../../content/map/turf.min.js"></script>
    <!--    <script src="../../../content/map/lnglat_to_gc.js"></script>-->
    <script src="../../../content/map/GeoAlgorithm.js"></script>


    <style>
        #map {
            width: 100%;
            height: 930px;
            /*height: calc(100% - 40px);*/
            overflow: hidden;
            position: relative;
            outline: 0;
        }

        body {
            margin: 0px;
        }

        .ol-overlaycontainer-stopevent
        {
            display:none;
        }

    </style>
</head>

<body>
<div id="map"></div>
</body>

</html>

<script type="text/javascript">

    var projectId = getQueryString("projectId");
    var longitude = getQueryString("longitude");
    var latitude = getQueryString("latitude");
    var id = getQueryString("id");
    var gqid = "";
    var djid = "";


    if(!longitude){
        longitude =  '118.219'
    }
    if(!latitude){
        latitude = '37.490'
    }
    //初始化地图
    var stamen = new ol.layer.Tile(
        {
            title: "Watercolor",
            baseLayer: true,
            // source: new ol.source.Stamen({
            //     layer: 'watercolor'
            // })

            source: new ol.source.XYZ({
                url: './roadmap/{z}/{x}/{y}.png'//本例中地图瓦片保存在当前目录下的tile文件夹目录下
            }),

        });

    // GeoJSON layer with a preview attribute
    var vectorSource = new ol.source.Vector(
        {
            features: [],
            wrapX: false
        });

    var vectorDjSource = new ol.source.Vector(
        {
            features: [],
            wrapX: false
        });


    var vectorQySource = new ol.source.Vector(
        {
            features: [],
            wrapX: false
        });

    var dylist = [];
    var djlist = [];
    var map = null;
    var vectorDjLayer ;
    var vectorQyLayer ;
    var djzbd;
    //初始化
    $(function () {
        reloadmap();
    });
    //初始化实例信息
    function initDyData(type,lo,la,id){

        var  featuresDy = vectorDyLayer.getSource().getFeatures()
        for (var i = 0; i < featuresDy.length; i++) {
            for(var k = 0; k < featuresDy[i].values_.features.length; k++){
                featuresDy[i].values_.features[k].values_.selected = false;
                featuresDy[i].values_.features[k].values_.marker = false;
            }

        }
        //获取单元全部坐标
        ajr_ajax_get("/slk/getSlkList", {id:id,type:type,longitude:lo,latitude:la}, (data) => {
            if (data.code == "0" || data.code == 0) {
                // 设置数据
                dylist = [];
                if (data.data.length > 0) {
                    var features = [];
                    for (var k = 0; k < data.data.length; k++) {

                        for (var i = 0; i < featuresDy.length; i++) {

                            for(var j = 0; j < featuresDy[i].values_.features.length; j++){
                                if(featuresDy[i].values_.features[j].values_.id == data.data[k].id){
                                    if(data.data[k].marker){
                                        featuresDy[i].values_.features[j].values_.selected = true;
                                        featuresDy[i].values_.features[j].values_.marker = true;
                                        if(1==type){
                                            var area = [];
                                            if (!isNull(data.data[k].slzbd)) {
                                                area =  data.data[k].slzbd.split(',');
                                            }
                                            if (area.length > 0 && area.length % 2 == 0) {
                                                var points = [];
                                                for (var j = 0; j < area.length; j += 2) {
                                                    var x = Number(area[j]);
                                                    var y = Number(area[j + 1]);
                                                    var point = {
                                                        x: x,
                                                        y: y
                                                    }
                                                    points.push(point);
                                                }
                                                var polygonCoords = calcMaxPolygon(points,null, 0);
                                                var plygon = new ol.geom.Polygon([polygonCoords]);
                                                var feature = new ol.Feature({
                                                    geometry: plygon,
                                                });
                                                features.push(feature);
                                            }
                                        }
                                    }
                                }
                            }

                        }
                    }


                    //清除样式
                    vectorQyLayer.getSource().clear();
                    vectorQySource.addFeatures(features);
                    vectorQyLayer.changed();
                    vectorDyLayer.setStyle(new MapStyle().createDyStyle(''));
                    vectorDyLayer.changed();
                    // initMap()
                }
            } else {
                layer.msg(data.data);
            }
        }, (e) => {
            console.log(e);
        });
        // vectorDyLayer.changed();
        map.getView().setCenter(ol.proj.fromLonLat([lo, la]));
    };
    //初始化单井信息
    function initDjData(){
        //清除样式
        vectorDyLayer.getSource().clear();
        //清除样式
        vectorDjLayer.getSource().clear();
        reloadmap();
    }
    //新建井 添加数据
    function addWell(name,jb,id,x,y){


        var wellFeature = new ol.Feature({
            geometry: new ol.geom.Point(ol.proj.fromLonLat([x, y])),
            visible: true,
            id:id,
            jb:jb,
            selected: false,
            name: name,
            type: 'well',
            marker:false,
        });
        wellFeature.setId(id);

        vectorDjLayer.getSource().addFeature(wellFeature)
        vectorDjLayer.changed();

    }

    //新建实例 添加数据
    function addUnit(name,id,x,y){

        var unitFeature = new ol.Feature({
            geometry: new ol.geom.Point(ol.proj.fromLonLat([x, y])),

            visible: true,
            id:id,
            selected: true,
            name: name,
            type: 'unit',
            marker:false,
        });
        map.getView().setCenter(ol.proj.fromLonLat([x, y]));
        vectorSource.addFeature(unitFeature);
        vectorDyLayer.changed();
    }

    //初始化获取坐标
    function reloadmap(type,lo,la) {
        //实例库内容页跳转获取坐标
        if (id) {
            ajr_ajax_get("/slk/getSlkInfo", {projectId:id}, (data) => {
                if (data.code == "0" || data.code == 0) {
                    gqid = data.data.id;
                    if(type != 1){
                        longitude = data.data.longitude;
                        latitude = data.data.latitude;
                    }
                } else {
                    layer.msg(data.data);
                }
            }, (e) => {
                console.log(e);
            });
        }

        //获取单元全部坐标
        ajr_ajax_get("/slk/getSlkList", {type:type,longitude:lo,latitude:la}, (data) => {
            if (data.code == "0" || data.code == 0) {
                // 设置数据
                dylist = [];
                if (data.data.length > 0) {

                    for (var k = 0; k < data.data.length; k++) {
                        dylist.push({
                            name: data.data[k].oilName,
                            marker: data.data[k].marker,
                            id: data.data[k].id,
                            X: data.data[k].longitude,
                            Y: data.data[k].latitude,
                        });

                        if(id){
                            if(data.data[k].id == id){
                                djzbd = data.data[k].slzbd;
                            }
                        }
                    }
                    initMap();
                    this.loadDjData(12582)
                }
            } else {
                layer.msg(data.data);
            }
        }, (e) => {
            console.log(e);
        });
        initMap();
    }
    var vectorDyLayer;

    function loadDjData(total){

        //单井
        var totalPage = 0;
        var limit = 12582;
        if (total % limit == 0) {
            totalPage = total / limit;
        } else {
            totalPage = parseInt(total / limit) + 1;
        }
        function ThreadFunc(opt, done) {
            var next_oReq = new XMLHttpRequest();
            next_oReq.open("post", opt.url, false); // 同步请求
            next_oReq.setRequestHeader("CONTENT-TYPE", "application/x-www-form-urlencoded");
            next_oReq.setRequestHeader("Authorization", opt.authorization);
            //var data = 'pageNo='+opt.pageNo+'&pageSize='+opt.pageSize;//+'&gqid=afef681e-b8e0-4f7c-8b1a-799bcc8a7931';
            var data = 'page='+opt.pageNo+'&limit='+opt.pageSize;
            next_oReq.send(data);

            var json = JSON.parse(next_oReq.responseText);
            var ret = json[Object.keys(json)[0]];
            done(ret);
        }

        var pool = new ThreadPool(totalPage);
        var first = true;
        for(var i = 1; i <= totalPage; i ++) {

            var option = {
                authorization: __getCookie("Authorization"),
                //url: serverapi + "/xmgl/getGqjhByCondition",
                url: serverapi + "/yt/getAllDataForCross",
                pageNo: i,
                pageSize: limit
            };

            pool.run(ThreadFunc,option,function (res) {
                console.log(res);
                if (res.code == 0 || res.code == "0") {
                    addJhPoint(res.rows);
                }
            });
        }
    }

    function addJhPoint(array) {
        // debugger
        var that = this;
        if(array != null && array.length > 0) {
            var wellFeatures = [];
            djlist = [];
            for (var i = 0; i < array.length; i++) {
                var temp = array[i];
                temp.lx = 0;
                try
                {
                    var pt = null;
                    if (temp.x > 10000) {
                        pt = new ol.geom.Point(ol.proj.fromLonLat(xian80_to_gps84(temp.x,temp.y)));
                    }else{
                        pt = new ol.geom.Point(ol.proj.fromLonLat([temp.x,temp.y]));
                    }
                    djlist.push({
                        name:temp.JH,
                        jb: temp.JB,
                        id:  temp.JHDM,
                        pt:pt
                    });
                }catch(e){
                    temp.zb_invalid = 1;
                }
            }
            initDj()

        }
    }
    //初始化
    function initMap() {
        //单元
        for (var i = 0; i < dylist.length; i++) {
            var dy = dylist[i];
            var pt = new ol.geom.Point(ol.proj.fromLonLat([dy.X, dy.Y]));
            var unitFeature = new ol.Feature({
                geometry: pt,
                visible: true,
                id:dy.id,
                selected: gqid == dy.id,
                name: dy.name,
                type: 'unit',
                marker:dy.marker,
            });
            unitFeature.setId(dy.id);
            vectorSource.addFeature(unitFeature);
        }

        if(vectorDyLayer){
            vectorDyLayer.changed();
            // vectorDjLayer.changed();
        }else{
            var clusterSource = new ol.source.Cluster({
                distance: 40,
                source: vectorSource,

            });

            vectorDyLayer = new ol.layer.Vector({
                name: '单元',
                zIndex: 1,
                source: clusterSource,

            });

            vectorQyLayer = new ol.layer.Vector({
                name: '区域',
                zIndex: 0,
                source: vectorQySource
            });

            vectorDyLayer.setStyle(new MapStyle().createDyStyle(''));
            // vectorDjLayer.setStyle(createDjStyle);
            vectorQyLayer.setStyle(new MapStyle().createQyStyle(''));

            var mapZoom = 8;
            if(id){
                mapZoom = 8;
            }
            var scaleLineControl=new ol.control.ScaleLine({
                Units: 'metric',//单位有5种：degrees imperial us nautical metric
            });//定义比例尺控件
            map = new ol.Map({
                target: 'map',
                view: new ol.View({
                    // zoom: 10,
                    zoom: mapZoom,
                    center: ol.proj.fromLonLat([longitude, latitude]),
                }),
                ontrols: [
                    new ol.control.LeftSearchBox({}),
                ],
                layers: [stamen,vectorDyLayer,vectorQyLayer],
            });
            // map.addControl(scaleLineControl);

            //坐标点击事件
            map.on('click', function(e) {

                var  featuresDy = vectorDyLayer.getSource().getFeatures()
                for (var i = 0; i < featuresDy.length; i++) {
                    for(var k = 0; k < featuresDy[i].values_.features.length; k++){
                        featuresDy[i].values_.features[k].values_.selected = false;
                        featuresDy[i].values_.features[k].values_.marker = false;
                    }
                }

                var drawCoord = e.coordinate;

                var poi = { x: drawCoord[0], y: drawCoord[1] };
                var point = _getLngLat(poi);

                var feature = map.forEachFeatureAtPixel(e.pixel, function(feature) {
                    return feature;
                });
                //清除样式
                vectorQyLayer.getSource().clear();
                vectorQyLayer.changed();
                // vectorDyLayer.getSource().clear();

                //不为空  坐标选中
                if(feature){


                    var info=feature.get("features")[0].values_
                    var type = info.type
                    if('unit' == type){
                        window.parent.gisDyClick(info.id,info.name)
                        longitude =  info.longitude
                        latitude =  info.latitude
                        info.selected = true
                        djid = ''
                        gqid = info.id
                        vectorDyLayer.setStyle(new MapStyle().createDyStyle(''));
                        vectorDyLayer.changed();
                    }else{
                        // window.parent.gisDjClick()
                        // info.selected = true
                        // djid =  info.id
                        // gqid = ''
                    }
                }else{
                    window.parent.gisEmptyClick()
                    window.parent.lo = point[0]
                    window.parent.la = point[1]
                }
            })

        }

        if(djzbd){
            var view =map.getView()
            var area = [];
            area =  djzbd.split(',');
            var features = [];
            if (area.length > 0 && area.length % 2 == 0) {
                var points = [];
                for (var j = 0; j < area.length; j += 2) {

                    var x = Number(area[j]);
                    var y = Number(area[j + 1]);
                    var point = {
                        x: x,
                        y: y
                    }
                    points.push(point);
                }
                var polygonCoords = calcMaxPolygon(points,null, 0);
                var plygon = new ol.geom.Polygon([polygonCoords]);


                view.fit(plygon,{
                    constrainResolution: true,
                    nearest: true,
                    padding: [50, 50, 50, 50],
                    size: [700, 700]
                })
                var feature = new ol.Feature({
                    geometry: plygon,
                });
                features.push(feature);

                vectorQyLayer.getSource().clear();
                vectorQySource.addFeatures(features);
                vectorQyLayer.changed();
            }

        }
        map.getView().setCenter(ol.proj.fromLonLat([longitude, latitude]));
    }
    function initDj(){
        if (vectorDjLayer!=undefined){
            return;
        }
        for (var i = 0; i < djlist.length; i++) {
            var dj = djlist[i];
            var wellFeature = new ol.Feature({
                geometry:dj.pt,
                visible: true,
                id:dj.id,
                jb:dj.jb,
                selected: dj.id == djid,
                name: dj.name,
                type: 'well',
                // marker:dj.marker,
            });
            wellFeature.setId(dj.id);
            vectorDjSource.addFeature(wellFeature);
        }
        var clusterSource = new ol.source.Cluster({
            distance: 40,
            source: vectorDjSource,

        });
        //加载聚合标注的矢量图层
        vectorDjLayer = new ol.layer.Vector({
            // name: '单井',
            // zIndex: 2,
            source: clusterSource,
            style: function (feature, resolution) {
                var size = feature.get('features').length;
                if(size==1){
                    var info=feature.values_.features[0].values_
                    var jhName = info.name;
                    var visible = info.visible;
                    if (!visible) return [];
                    var marker = info.marker;
                    var jb ='';
                    if(info.jb){
                        jb = info.jb.toUpperCase();
                    }
                    var color = 'red';
                    if (jb == "SJ") color = 'blue';
                    else if (jb == "QJ") color = 'yellow';
                    var textcolor = '#000000';
                    var radius = 4;
                    var selected = info.selected;
                    var font =  'bold 12px  "Open Sans", "Arial Unicode MS", "sans-serif"'
                    if (selected) {
                        textcolor = 'red';
                        radius = 5;

                        font =  'bold 14px  "Open Sans", "Arial Unicode MS", "sans-serif"'
                    }
                    var pointStyle = new ol.style.Circle({
                        radius: radius,
                        fill: new ol.style.Fill({ //矢量图层填充颜色，以及透明度
                            color: color
                        }),
                        stroke: new ol.style.Stroke({
                            color: color,
                            width: 1
                        })
                    });
                    if (marker) {
                        pointStyle = new ol.style.Icon(({
                            src: 'res/marker.png',
                            imgSize: [24, 24]
                        }));
                        if (selected) {
                            pointStyle = new ol.style.Icon(({
                                src: 'res/marker32.png',
                                imgSize: [32, 32]
                            }));
                            textcolor = '#000000';
                        }
                    }
                    var djtextStyle = new ol.style.Text({
                        text: jhName,
                        textAlign: 'left', //位置
                        textBaseline: 'middle', //基准线
                        offsetX: 5,
                        offsetY: 1,
                        font: font,
                        placement: 'point',
                        rotation: 0,
                        fill: new ol.style.Fill({
                            color: textcolor
                        }),
                        stroke: new ol.style.Stroke({
                            color: '#000000',
                            width: 0.00000001
                        })
                    });
                    var stylesjh = new ol.style.Style({
                        image: pointStyle
                        , text: djtextStyle
                    });

                    var styles = [stylesjh];
                    return styles;
                } else{
                    return new ol.style.Style({
                        image: new ol.style.Icon(/** @type {olx.style.IconOptions} */({
                            anchor: [0.5, 60],
                            anchorOrigin: 'top-right',
                            anchorXUnits: 'fraction',
                            anchorYUnits: 'pixels',
                            offsetOrigin: 'top-right',
                            offset: [0, 1],//偏移量设置
                            scale: 0.03,  //图标缩放比例
                            opacity: 1,  //透明度
                            src: 'img/red.png'//图标的url
                        })),
                        text: new ol.style.Text({
                            // text: size.toString(),
                            offsetX: 0,
                            offsetY: 11,
                            fill: new ol.style.Fill({
                                color: '#000000'
                            })

                        })
                    });
                }
            }
        });
        map.addLayer(vectorDjLayer)
    }

    function setMapCenter(opt) {
        map.getView().setCenter(ol.proj.fromLonLat([parseInt(opt.X), parseInt(opt.Y)]));
    }
</script>
