
<script>
    //location.href = "http://127.0.0.1:8090/sys/emailLogin";
</script>

<!DOCTYPE html>
<html lang="zh-cn">

<head>

    <meta charset="UTF-8">

    <title>中国石化油田开发决策支持系统</title>

    <link rel="shortcut icon " type="images/x-icon" href="../ico/tab/v5.png">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <script src="../content/js/jquery-1.10.2.js"></script>
    <script src="../content/js/vue.js"></script>

    <!--<script src="../content/js/index.js"></script>-->

    <link href="../content/libs/layui/css/layui.css" rel="stylesheet" />
    <link href="../content/common.css" rel="stylesheet" />

    <script src="../content/libs/layui/layui.all.js"></script>
    <script src="../content/common.js"></script>

    <script>

        var urlPath = window.document.location.href;
        var docPath = window.document.location.pathname;
        var index = urlPath.indexOf(docPath);
        var serverPath = urlPath.substring(0, index);

        if (serverPath.indexOf(':') > -1) {
            serverPath = serverPath.substring(0, serverPath.lastIndexOf(':'));
        }

    </script>

    <style type="text/css">

        html{
            font-size:10px;
        }

        body {
            background: url(../content/login/ajr_bjt1.png);
            background-repeat: no-repeat;
            background-size: cover;
        }

        #login-page:after {
            content: '';
            /*  background: url(resources/images/login_backgrounds.png);*/
            background-repeat: no-repeat;
            background-size: cover;
            -webkit-filter: blur(3px);
            -moz-filter: blur(3px);
            -o-filter: blur(3px);
            -ms-filter: blur(3px);
            filter: blur(3px);
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
        }

        .logo-title {
            text-align: center;
            letter-spacing: 2px;
            padding: 14px 0;
        }

        .logo-title h1 {
            color: #007DDB;
            font-size: 32px;
            font-weight: 400;
        }

        #login-form {
            background-color: #fff;
            border: 1px solid #fff;
            border-radius: 3px;
            padding: 14px 20px;
            box-shadow: 0 0 8px #eeeeee;
        }

        #login-form .layui-form-item {
            position: relative;
        }

        #login-form .layui-form-item label {
            position: absolute;
            left: 1px;
            top: 1px;
            width: 38px;
            line-height: 36px;
            text-align: center;
            color: #d2d2d2;
        }

        #login-form .layui-form-item input {
            padding-left: 36px;
        }

        #Captcha {
            width: 60%;
            display: inline-block;
        }

        .captcha-img {
            display: inline-block;
            width: 34%;
            float: right;
        }

        .captcha-img img {
            height: 34px;
            border: 1px solid #e6e6e6;
            height: 36px;
            width: 100%;
        }

        .tool_check {
            height: 20px;
            width: 14px;
            margin-left: 10px;
            vertical-align: middle;
        }

        .tool_input {
            padding: 5px;
            background-color: #F2F2F2;
            border: 2px #E6E6E6 solid;
        }

        .tool_input input {
            margin-bottom: 3px;
        }

        .tool_input i {
            margin-left: 15px;
            margin-bottom: 3px;
        }

        .tool_input a {

        }

        .tool_input a:hover {
            text-decoration: underline;
            cursor: pointer;
        }

        .layui-form {
            width: 320px;
            float: right;
            vertical-align:bottom;
        }

        .layui-btn1 {
            display: inline-block;
            height: 38px;
            line-height: 38px;
            padding: 0 18px;
            background-color: #007DDB;
            color: #fff;
            white-space: nowrap;
            text-align: center;
            font-size: 14px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
        }

    </style>

    <script src="../content/js/common.js" type="text/javascript"></script>

</head>

<body id="login-page">

    <div style="font-size: 3.2rem;
                    color: white;
                    margin: 26px 50px;
                    display: inline-block;
                    line-height: 60px;
                    height: 60px;
                    width: 100%;">

        <img src="../content/images/ajr_logo.png"
             width="35"
             style="vertical-align: sub;" />
        <span>
            实例库管理与智能应用
        </span>
        <div style="float:right;margin-right: 80px;display: block;">
            <span style="display:inline-block;"><button class="layui-btn1 layui-btn-fluid" id="chrome"
                    >Chrome</button></span>
            <span style="display:inline-block;"><button class="layui-btn1 layui-btn-fluid" id="dotnet"
                    >.Net</button></span>
        </div>
    </div>

    <div class="layui-container" style="width: 76%;">

        <div class="layui-row layui-row-space10">

            <div class="layui-form" id="login-form" style="margin-top: 20%;">

                <form class="layui-form" action="">

                    <!--
                    <div class="layui-form-item logo-title">
                        <h1>用户登录</h1>
                    </div>
                    -->

                    <div class="layui-form-item">
                        <label class="layui-icon layui-icon-username" for="UserName"></label>
                        <input type="text"
                               name="UserName"
                               id="UserName"
                               lay-verify="required|account"
                               placeholder="用户名或者邮箱"
                               autocomplete="off"
                               class="layui-input"
                               value="xuliang">
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-icon layui-icon-password"
                               for="Password"></label>
                        <input type="password"
                               name="Password"
                               id="Password"
                               lay-verify="required|password"
                               placeholder="密码"
                               autocomplete="off"
                               class="layui-input"
                               value="123456">
                    </div>

                    <div class="layui-form-item"
                         style="display:none;">
                        <label class="layui-icon layui-icon-vercode"
                               for="Captcha"></label>
                        <input type="text"
                               name="Captcha"
                               id="Captcha"
                               lay-verify="required|captcha"
                               placeholder="图形验证码"
                               autocomplete="off"
                               class="layui-input verification"
                               value="1234">
                        <div class="captcha-img">
                            <img id="captchaPic" src="">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <input type="checkbox"
                               name="rememberMe"
                               value="true"
                               checked="checked"
                               lay-skin="primary"
                               title="记住密码">

<!--                        <a onclick="log();"-->
<!--                           style="float:right;padding-top: 10px;padding-right: 10px; color: #007DDB;cursor:pointer;">统一认证登录</a>-->

                        <a href="register1.html"
                           style="float:right;padding-top: 10px;padding-right: 10px; color: #007DDB;display:none;">注册新用户</a>

                    </div>

                    <div class="layui-form-item">
                        <button class="layui-btn layui-btn-fluid"
                                lay-submit
                                lay-filter="form-login">登 录</button>
                    </div>

                </form>
            </div>

        </div>

    </div>

    <div style="text-align: center;
                position: absolute;
                bottom: 10px;
                width: 100%;
                color: #b2b2b2;">
        版本所有 中国石化 胜利油田
    </div>

</body>

</html>

<script language="javascript">

    function log() {

        let idx_lay = layer.open({

            type: 2,
            skin: 'layui-layer-lan',
            area: ['80%', '80%'],
            fixed: false,
            scrollbar: false,

            title: '中国石化统一认证登录',

            maxmin: true,

            content: ['http://************:8085/dataApi/sys/emailLogin', 'no'],

            success: function (layero, index) {

            },

            yes: function (index, layero) {

            },

        });

    }

    $("#chrome")[0].onclick = function () {
        window.open("./ChromeStandaloneSetup64.exe", "_blank");
    };
    $("#dotnet")[0].onclick = function () {
        window.open("./dotNetFx40_Full_x86_x64.zip", "_blank");
    };

</script>

<script type="text/javascript">

        var uuid = "";

        function uuidfun() {

            var s = [];
            var hexDigits = "0123456789abcdef";

            for (var i = 0; i < 36; i++) {
                s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
            }

            s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
            s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
            s[8] = s[13] = s[18] = s[23] = "-";

            var uuid = s.join("");

            return uuid;

        }

        function uuidstr() {
            uuid = uuidfun();
            return uuid;
        }

        function setCookie_log(c_name, value, domain) {
            var exdate = new Date(),
                expiredays = 365;
            exdate.setDate(exdate.getDate() + expiredays);
            //判断是否需要跨域存储
            if (domain) {
                document.cookie = c_name + "=" + escape(value) + ((expiredays == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/;domain=" + domain;
            } else {
                document.cookie = c_name + "=" + escape(value) + ((expiredays == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
            }
        }

        layui.use(['jquery', 'form', 'layer', 'ajaxmod', 'verifymod'], function () {

            //var $ = layui.jquery,
            var form = layui.form,
                layer = layui.layer,
                ajaxmod = layui.ajaxmod,
                verifymod = layui.verifymod;

            if (localStorage.getItem('rememberme')) {
                var setval = JSON.parse(localStorage.getItem('rememberme'));
                $("input[name='UserName']").val(setval.username)
                $("input[name='Password']").val(setval.password)
            }

            // 字段校验
            form.verify(verifymod);

            //监听提交
            form.on('submit(form-login)', function (data) {

                data.field.CaptchaKey = uuid;

                data.field.userName = data.field.UserName;

                data.field.password = data.field.Password;

                ajr_ajax_post("/sys/login", data.field, function (res) {

                    if (res != null && res.success) {

                        var p = {
                            path: "/",
                        }

                        __setCookie("userName", res.data.userName);
                        __setCookie("userId", data.field.userName);
                        __setCookie("Authorization", res.data.Authorization);
                        __setCookie("Permission", res.data.menuData);
                        __setCookie("name", data.field.userName);

                        var reobj = {
                            username: data.field.UserName,
                            password: data.field.Password
                        };

                        if (data.field.rememberMe) {
                            localStorage.setItem("rememberme", JSON.stringify(reobj));
                        } else {
                            localStorage.removeItem("rememberme");
                        }

                        layer.msg('登录成功', {
                            icon: 1
                        });

                        //window.location.href = "./Home/Index.html";
                        window.location.href = "./slk/SlkHome.html";

                    }
                    else {

                        layer.msg("账户登录失败！", {
                            icon: 2
                        });

                        document.all.UserName.value = "";
                        document.all.Password.value = "";

                    }
                });

                return false;
            });
        });

</script>
